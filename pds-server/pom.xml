<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jh</groupId>
    <artifactId>pds-server</artifactId>
    <version>1.0-SNAPSHOT</version>

    <packaging>pom</packaging>
    <name>pds-server</name>

    <modules>
        <module>jh-start</module>
        <module>jh-constant</module>
        <module>jh-aop</module>
        <module>jh-utils</module>
        <module>jh-redis-msglistener</module>
        <module>module</module>
    </modules>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jjwt.version>0.12.6</jjwt.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <tk.mybatis.version>5.0.1</tk.mybatis.version>
        <github.pagehelper.version>2.1.0</github.pagehelper.version>
        <mybatis.spring.start.version>3.0.4</mybatis.spring.start.version>
        <security-oauth2.version>2.5.2.RELEASE</security-oauth2.version>
        <aliyun-sdk-oss.version>3.18.1</aliyun-sdk-oss.version>
        <io.version>2.12.0</io.version>
        <velocity.version>1.7</velocity.version>

        <druid.version>1.2.23</druid.version>
        <bcprov.version>1.70</bcprov.version>
        <hutool.version>5.8.35</hutool.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <alibaba-easyexcel.version>4.0.3</alibaba-easyexcel.version>
        <knife4j-swagger.version>4.5.0</knife4j-swagger.version>
        <!-- xss过滤-->
        <jsoup.version>1.18.3</jsoup.version>
        <!-- 验证码-->
        <kaptcha.version>2.3.2</kaptcha.version>
        <logstash-logback.version>8.0</logstash-logback.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <minio.version>8.5.14</minio.version>
        <spring.boot.version>3.4.6</spring.boot.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                <version>3.3.5</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>7.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.jh</groupId>
            <artifactId>jh-common</artifactId>
            <version>1.0.23</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


<build>
    <plugins>
        <plugin>
            <groupId>org.sonarsource.scanner.maven</groupId>
            <artifactId>sonar-maven-plugin</artifactId>
            <version>5.0.0.4389</version>
        </plugin>

    </plugins>

</build>

</project>