-- 为enterprise_verification表添加核验结果字段

-- 基本信息核验结果
ALTER TABLE enterprise_verification ADD COLUMN ENTERPRISE_NAME_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '企业名称核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN USCC_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '统一社会信用代码核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN LEGAL_REPRESENTATIVE_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '法定代表人核验结果：1-一致，0-不一致';

-- 食品生产许可证核验结果
ALTER TABLE enterprise_verification ADD COLUMN FOOD_ISSUING_AUTHORITY_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '食品生产许可证发证机关核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN FOOD_LICENSE_NUMBER_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '食品生产许可证编号核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN FOOD_ISSUE_DATE_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '食品生产许可证发证日期核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN FOOD_CERTIFICATE_STATUS_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '食品生产许可证状态核验结果：1-有效，0-无效';

-- 全国工业产品生产许可证核验结果
ALTER TABLE enterprise_verification ADD COLUMN INDUSTRIAL_ISSUE_DATE_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '工业产品许可证发证日期核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN INDUSTRIAL_CERTIFICATE_NUMBER_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '工业产品许可证编号核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN INDUSTRIAL_CERTIFICATE_STATUS_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '工业产品许可证状态核验结果：1-有效，0-无效';

-- 专利授权信息核验结果
ALTER TABLE enterprise_verification ADD COLUMN PATENT_TITLE_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '专利标题核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN PATENT_TYPE_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '专利类型核验结果：1-一致，0-不一致';
ALTER TABLE enterprise_verification ADD COLUMN APPLICANT_VERIFY_RESULT TINYINT(1) DEFAULT NULL COMMENT '申请人核验结果：1-一致，0-不一致';

-- 核验汇总信息
ALTER TABLE enterprise_verification ADD COLUMN VERIFY_STATUS TINYINT(1) DEFAULT 0 COMMENT '整体核验状态：0-未完成，1-已完成';
ALTER TABLE enterprise_verification ADD COLUMN VERIFY_TIME DATETIME DEFAULT NULL COMMENT '核验完成时间';
