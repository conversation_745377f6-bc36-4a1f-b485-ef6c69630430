package com.jh.pds.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字段校验枚举
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Getter
@AllArgsConstructor
public enum FieldVerificationEnum {

    // 基本信息校验
    ENTERPRISE_NAME("enterprise_name", "ENTERPRISE_NAME", VerificationTable.MARKET_ENTITY, false, null, "enterpriseNameVerifyResult"),
    USCC("uscc", "UNIFIED_SOCIAL_CREDIT_CODE", VerificationTable.MARKET_ENTITY, false, null, "usccVerifyResult"),
    LEGAL_REPRESENTATIVE("legal_representative", "LEGAL_REPRESENTATIVE", VerificationTable.MARKET_ENTITY, true, "UNIFIED_SOCIAL_CREDIT_CODE", "legalRepresentativeVerifyResult"),

    // 食品生产许可证校验
    FOOD_ISSUING_AUTHORITY("food_issuing_authority", "ISSUING_AUTHORITY", VerificationTable.FOOD_PRODUCTION_LICENSE, false, null, "foodIssuingAuthorityVerifyResult"),
    FOOD_LICENSE_NUMBER("food_license_number", "LICENSE_NUMBER", VerificationTable.FOOD_PRODUCTION_LICENSE, false, null, "foodLicenseNumberVerifyResult"),
    FOOD_ISSUE_DATE("food_issue_date", "ISSUE_DATE", VerificationTable.FOOD_PRODUCTION_LICENSE, true, "LICENSE_NUMBER", "foodIssueDateVerifyResult"),
    FOOD_CERTIFICATE_STATUS("food_certificate_status", null, VerificationTable.FOOD_PRODUCTION_LICENSE, true, null, "foodCertificateStatusVerifyResult"),

    // 全国工业产品生产许可证校验
    INDUSTRIAL_ISSUE_DATE("industrial_issue_date", "ISSUE_DATE", VerificationTable.INDUSTRIAL_PRODUCT_LICENSE, false, null, "industrialIssueDateVerifyResult"),
    INDUSTRIAL_CERTIFICATE_NUMBER("industrial_certificate_number", "CERTIFICATE_NUMBER", VerificationTable.INDUSTRIAL_PRODUCT_LICENSE, false, null, "industrialCertificateNumberVerifyResult"),
    INDUSTRIAL_CERTIFICATE_STATUS("industrial_certificate_status", null, VerificationTable.INDUSTRIAL_PRODUCT_LICENSE, true, null, "industrialCertificateStatusVerifyResult"),

    // 专利授权信息校验
    PATENT_TITLE("patent_title", "PATENT_TITLE", VerificationTable.PATENT_AUTHORIZATION, false, null, "patentTitleVerifyResult"),
    PATENT_TYPE("patent_type", "PATENT_TYPE", VerificationTable.PATENT_AUTHORIZATION, true, "PATENT_TITLE", "patentTypeVerifyResult"),
    APPLICANT("applicant", "APPLICANT", VerificationTable.PATENT_AUTHORIZATION, false, null, "applicantVerifyResult");

    /**
     * 字段类型（前端传入的字段标识）
     */
    private final String fieldType;

    /**
     * 数据库字段名
     */
    private final String dbFieldName;

    /**
     * 校验表类型
     */
    private final VerificationTable table;

    /**
     * 是否需要附加参数
     */
    private final boolean needAdditionalParam;

    /**
     * 附加字段名
     */
    private final String additionalFieldName;

    /**
     * 核验结果字段名（实体类中的属性名）
     */
    private final String verifyResultFieldName;

    /**
     * 根据字段类型获取枚举
     * 
     * @param fieldType 字段类型
     * @return 对应的枚举，如果不存在返回null
     */
    public static FieldVerificationEnum getByFieldType(String fieldType) {
        for (FieldVerificationEnum field : values()) {
            if (field.getFieldType().equals(fieldType)) {
                return field;
            }
        }
        return null;
    }

    /**
     * 是否为证书状态字段
     * 
     * @return true表示是证书状态字段
     */
    public boolean isCertificateStatus() {
        return this == FOOD_CERTIFICATE_STATUS || this == INDUSTRIAL_CERTIFICATE_STATUS;
    }

    /**
     * 校验表枚举
     */
    @Getter
    @AllArgsConstructor
    public enum VerificationTable {
        MARKET_ENTITY("市场主体信息"),
        FOOD_PRODUCTION_LICENSE("食品生产许可证"),
        INDUSTRIAL_PRODUCT_LICENSE("全国工业产品生产许可证"),
        PATENT_AUTHORIZATION("专利授权信息");

        private final String description;
    }
}
