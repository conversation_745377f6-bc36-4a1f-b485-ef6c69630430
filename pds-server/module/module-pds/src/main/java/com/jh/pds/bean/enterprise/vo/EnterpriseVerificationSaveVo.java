package com.jh.pds.bean.enterprise.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 企业信息核验保存VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Schema(description = "企业信息核验保存VO")
@Data
public class EnterpriseVerificationSaveVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（更新时需要）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    // 基本信息
    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "统一社会信用代码")
    private String uscc;

    @Schema(description = "法定代表人")
    private String legalRepresentative;

    // 食品生产许可证信息
    @Schema(description = "食品生产许可证发证机关")
    private String foodIssuingAuthority;

    @Schema(description = "食品生产许可证编号")
    private String foodLicenseNumber;

    @Schema(description = "食品生产许可证发证日期")
    private Date foodIssueDate;

    @Schema(description = "食品生产许可证状态")
    private String foodCertificateStatus;

    // 全国工业产品生产许可证信息
    @Schema(description = "工业产品许可证发证日期")
    private Date industrialIssueDate;

    @Schema(description = "工业产品许可证编号")
    private String industrialCertificateNumber;

    @Schema(description = "工业产品许可证状态")
    private String industrialCertificateStatus;

    // 专利授权信息
    @Schema(description = "专利标题")
    private String patentTitle;

    @Schema(description = "专利类型")
    private String patentType;

    @Schema(description = "申请人")
    private String applicant;

    // 核验结果（key为字段类型，value为核验结果：1-一致，0-不一致）
    @Schema(description = "核验结果Map，key为字段类型，value为核验结果")
    private Map<String, Integer> verifyResults;

    /**
     * 是否完成核验
     */
    @Schema(description = "是否完成核验")
    private Boolean isCompleted;

}
