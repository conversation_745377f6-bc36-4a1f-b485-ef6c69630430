package com.jh.pds.service.enterprise;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.pds.bean.enterprise.EnterpriseVerification;
import com.jh.pds.bean.enterprise.vo.EnterpriseVerificationVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 企业基本信息核验Service接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface EnterpriseVerificationService {
    /**
     * 保存或更新企业基本信息核验
     *
     * @param enterpriseVerification 企业基本信息核验对象
     * @return String 企业基本信息核验ID
     * <AUTHOR>
     */
    String saveOrUpdateEnterpriseVerification(EnterpriseVerification enterpriseVerification);

    /**
     * 删除企业基本信息核验
     *
     * @param ids void 企业基本信息核验ID
     * <AUTHOR>
     */
    void deleteEnterpriseVerification(List<String> ids);

    /**
     * 查询企业基本信息核验详情
     *
     * @param id
     * @return EnterpriseVerification
     * <AUTHOR>
     */
    EnterpriseVerification findById(String id);

    /**
     * 分页查询企业基本信息核验
     *
     * @param enterpriseVerificationVo
     * @return PageInfo<EnterpriseVerification>
     * <AUTHOR>
     */
    PageInfo<EnterpriseVerification> findPageByQuery(EnterpriseVerificationVo enterpriseVerificationVo);

    /**
     * 校验企业信息字段
     *
     * @param fieldType 字段类型
     * @param fieldValue 字段值
     * @param additionalParam 附加参数
     * @return Map<String, Object> 校验结果
     * <AUTHOR>
     */
    Map<String, Object> verifyField(String fieldType, String fieldValue, String additionalParam);

}
