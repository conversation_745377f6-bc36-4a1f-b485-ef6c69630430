package com.jh.pds.service.enterprise;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.pds.bean.enterprise.EnterpriseVerification;
import com.jh.pds.bean.enterprise.vo.EnterpriseVerificationVo;
import com.jh.pds.bean.enterprise.vo.FieldVerificationVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 企业基本信息核验Service接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface EnterpriseVerificationService {


    /**
     * 删除企业基本信息核验
     *
     * @param ids void 企业基本信息核验ID
     * <AUTHOR>
     */
    void deleteEnterpriseVerification(List<String> ids);

    /**
     * 查询企业基本信息核验详情
     *
     * @param id
     * @return EnterpriseVerification
     * <AUTHOR>
     */
    EnterpriseVerification findById(String id);

    /**
     * 分页查询企业基本信息核验
     *
     * @param enterpriseVerificationVo
     * @return PageInfo<EnterpriseVerification>
     * <AUTHOR>
     */
    PageInfo<EnterpriseVerification> findPageByQuery(EnterpriseVerificationVo enterpriseVerificationVo);

    /**
     * 校验企业信息字段
     *
     * @param verificationVo 校验请求参数
     * @return Integer 校验结果：1表示一致，0表示不一致
     * <AUTHOR>
     */
    Integer verifyField(FieldVerificationVo verificationVo);

    /**
     * 保存企业信息核验记录（包含核验结果）
     *
     * @param verificationVo 保存请求参数
     * @return String 企业基本信息核验ID
     * <AUTHOR>
     */
    String saveEnterpriseVerificationWithResults(EnterpriseVerificationVo verificationVo);

}
