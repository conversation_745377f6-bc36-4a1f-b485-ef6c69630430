package com.jh.pds.enums.enterprise;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核验结果枚举
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Getter
@AllArgsConstructor
public enum VerifyResultEnum {

    /**
     * 一致/有效
     */
    CONSISTENT(1, "一致"),

    /**
     * 不一致/无效
     */
    INCONSISTENT(0, "不一致"),

    /**
     * 未核验
     */
    NOT_VERIFIED(null, "未核验");

    /**
     * 结果代码
     */
    private final Integer code;

    /**
     * 结果描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     * 
     * @param code 结果代码
     * @return 对应的枚举，如果不存在返回NOT_VERIFIED
     */
    public static VerifyResultEnum getByCode(Integer code) {
        if (code == null) {
            return NOT_VERIFIED;
        }
        for (VerifyResultEnum result : values()) {
            if (code.equals(result.getCode())) {
                return result;
            }
        }
        return NOT_VERIFIED;
    }

    /**
     * 是否为一致/有效
     * 
     * @return true表示一致/有效
     */
    public boolean isConsistent() {
        return this == CONSISTENT;
    }

    /**
     * 是否已核验
     * 
     * @return true表示已核验（不管结果如何）
     */
    public boolean isVerified() {
        return this != NOT_VERIFIED;
    }
}
