package com.jh.pds.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风险等级枚举
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Getter
@AllArgsConstructor
public enum RiskLevelEnum {

    /**
     * 高风险
     */
    HIGH("高风险", 80, 100, "red"),

    /**
     * 中高风险
     */
    MEDIUM_HIGH("中高风险", 60, 79, "orange"),

    /**
     * 中风险
     */
    MEDIUM("中风险", 40, 59, "yellow"),

    /**
     * 中低风险
     */
    MEDIUM_LOW("中低风险", 20, 39, "blue"),

    /**
     * 低风险
     */
    LOW("低风险", 1, 19, "green");

    /**
     * 风险等级名称
     */
    private final String levelName;

    /**
     * 最低分数
     */
    private final int minScore;

    /**
     * 最高分数
     */
    private final int maxScore;

    /**
     * 颜色标识
     */
    private final String color;

    /**
     * 根据分数获取风险等级
     * 
     * @param score 风险评分
     * @return 对应的风险等级
     */
    public static RiskLevelEnum getByScore(int score) {
        for (RiskLevelEnum level : values()) {
            if (score >= level.getMinScore() && score <= level.getMaxScore()) {
                return level;
            }
        }
        return LOW; // 默认返回低风险
    }

    /**
     * 是否为高风险
     * 
     * @return true表示高风险
     */
    public boolean isHighRisk() {
        return this == HIGH || this == MEDIUM_HIGH;
    }
}
