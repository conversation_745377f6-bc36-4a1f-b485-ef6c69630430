package com.jh.pds.bean.enterprise.vo;

import com.jh.pds.bean.enterprise.EnterpriseVerification;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 企业基本信息核验Vo
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Schema(description = "EnterpriseVerificationVo")
@Data
public class EnterpriseVerificationVo extends EnterpriseVerification {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    /**
     * 字段类型（enterprise_name, uscc, legal_representative等）
     */
    @Schema(description = "字段类型")
    private String fieldType;

    /**
     * 字段值
     */
    @Schema(description = "字段值")
    private String fieldValue;

    /**
     * 附加参数（如统一社会信用代码，用于关联查询）
     */
    @Schema(description = "附加参数")
    private String additionalParam;

}