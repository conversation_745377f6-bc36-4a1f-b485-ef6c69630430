package com.jh.jh.service.impl;

import com.jh.jh.bean.vo.EnterpriseVerificationExcel;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.jh.bean.EnterpriseVerification;
import com.jh.jh.bean.vo.EnterpriseVerificationVo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.jh.dao.EnterpriseVerificationMapper;
import com.jh.jh.service.EnterpriseVerificationService;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import static com.github.pagehelper.page.PageMethod.orderBy;
import org.springframework.web.multipart.MultipartFile;

/**
 *  企业基本信息核验Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
@Transactional(readOnly = true)
public class EnterpriseVerificationServiceImpl extends BaseServiceImpl<EnterpriseVerificationMapper, EnterpriseVerification> implements EnterpriseVerificationService{
	
	private static final Logger logger = LoggerFactory.getLogger(EnterpriseVerificationServiceImpl.class);
    @Autowired
    private EnterpriseVerificationMapper enterpriseVerificationMapper;

    @Autowired
	private ImportService importService;

	@Value("${file.temp.path}")
	private String tempPath;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新企业基本信息核验
	 *@param enterpriseVerification 企业基本信息核验对象
	 *@return String 企业基本信息核验ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateEnterpriseVerification(EnterpriseVerification enterpriseVerification) {
		if(enterpriseVerification==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(enterpriseVerification.getId())){
			//新增
			enterpriseVerification.setId(UUIDUtils.getUUID());
			enterpriseVerificationMapper.insertSelective(enterpriseVerification);
		}else{
			//TODO 做判断后方能执行修改 一般判断是否是自己的数据
			//避免页面传入修改
			enterpriseVerification.setYn(null);
			enterpriseVerificationMapper.updateByPrimaryKeySelective(enterpriseVerification);
		}
		return enterpriseVerification.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除企业基本信息核验
	 *@param ids void 企业基本信息核验ID
	 *<AUTHOR>
	 */
	public void deleteEnterpriseVerification(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除 一般判断是否是自己的数据
			EnterpriseVerification enterpriseVerification=enterpriseVerificationMapper.selectByPrimaryKey(id);
			if(enterpriseVerification==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			EnterpriseVerification tementerpriseVerification=new EnterpriseVerification();
			tementerpriseVerification.setYn(CommonConstant.FLAG_NO);
			tementerpriseVerification.setId(enterpriseVerification.getId());
			enterpriseVerificationMapper.updateByPrimaryKeySelective(tementerpriseVerification);
		}
	}

	/**
	 * 查询企业基本信息核验详情
	 *@param id
	 *@return EnterpriseVerification
	 *<AUTHOR>
	 */
    @Override
	public EnterpriseVerification findById(String id) {
	    // TODO 做判断后方能反回数据 一般判断是否是自己的数据
		return enterpriseVerificationMapper.selectByPrimaryKey(id);
	}


	/**
	 * 分页查询企业基本信息核验
	 *@param enterpriseVerificationVo
	 *@return PageInfo<EnterpriseVerification>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<EnterpriseVerification> findPageByQuery(EnterpriseVerificationVo enterpriseVerificationVo) {
		// TODO 做判断后方能执行查询 一般判断是否是自己的数据
		PageHelper.startPage(enterpriseVerificationVo.getPageNum(),enterpriseVerificationVo.getPageSize());
		orderBy(enterpriseVerificationVo.getOrderColumn() + " " + enterpriseVerificationVo.getOrderValue());
		Example example=getExample(enterpriseVerificationVo);
		List<EnterpriseVerification> enterpriseVerificationList=enterpriseVerificationMapper.selectByExample(example);
		return new PageInfo<EnterpriseVerification>(enterpriseVerificationList);
	}
	private Example getExample(EnterpriseVerificationVo enterpriseVerificationVo){
		Example example=new Example(EnterpriseVerification.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(enterpriseVerificationVo.getName())){
		//	criteria.andEqualTo(enterpriseVerificationVo.getName());
		//}
		example.orderBy("updateTime").desc();
		return example;
	}

	/**
     * 按条件导出查询企业基本信息核验
     *@param enterpriseVerificationVo
     *@return PageInfo<EnterpriseVerification>
     *<AUTHOR>
     */
    @Override
    public List<EnterpriseVerification> findByQuery(EnterpriseVerificationVo enterpriseVerificationVo){
    	orderBy(enterpriseVerificationVo.getOrderColumn() + " " + enterpriseVerificationVo.getOrderValue());
        Example example=getExample(enterpriseVerificationVo);
        return enterpriseVerificationMapper.selectByExample(example);
    }

    /**
     * 导入企业基本信息核验
     *@param file
     *@param cover 是否覆盖 1 覆盖 0 不覆盖
     *@return
     *<AUTHOR>
     */
    @Override
    public void importEnterpriseVerificationAsync(MultipartFile file, Integer cover) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser,"已创建企业基本信息核验导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL,"企业基本信息核验",null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<EnterpriseVerificationExcel> list;
        // 读取数据
        try(InputStream inputStream = file.getInputStream()){
            list = (List<EnterpriseVerificationExcel>) EasyExcelUtils.readExcel(EnterpriseVerificationExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容，如：日期格式");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID()+"&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                List<EnterpriseVerification> insertList = new ArrayList<>();
                //数据处理
                for(EnterpriseVerificationExcel item:list){
                    //这里对数据进行校验 TODO
                    if(StringUtils.isEmpty(item.getCode())){
                        item.setImportSituation(ImportStatusEnum.ERROR.getName()+"编码为空");
                        continue;
                    }

                    EnterpriseVerification enterpriseVerification = new EnterpriseVerification();
                    BeanUtils.copyProperties(item,enterpriseVerification);
                    enterpriseVerification.setId(UUIDUtils.getUUID());
                    enterpriseVerification.setYn(CommonConstant.FLAG_YES);
                    insertList.add(enterpriseVerification);
                    item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                }
                //保存数据
                if(!CollectionUtils.isEmpty(insertList)){
                    //这里处理是否覆盖 TODO
                    enterpriseVerificationMapper.insertList(insertList);
                }
                String fileUrl = importService.faultDataAsync(list, EnterpriseVerificationExcel.class, "导入结果.xls",
                        "企业基本信息核验", "企业基本信息核验", startTime, copyMultipartFile,appLoginUser);
                importService.notification(appLoginUser,"企业基本信息核验-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN,file.getOriginalFilename() + "导入成功",fileUrl);
            }catch (Exception e){
                importService.notification(appLoginUser,e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL,"企业基本信息核验导入【出现异常】",null);
            }finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }
}
