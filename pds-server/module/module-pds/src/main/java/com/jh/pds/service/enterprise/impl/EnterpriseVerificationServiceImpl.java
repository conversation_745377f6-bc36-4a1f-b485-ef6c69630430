package com.jh.pds.service.enterprise.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.pds.bean.enterprise.EnterpriseVerification;
import com.jh.pds.bean.enterprise.vo.EnterpriseVerificationVo;
import com.jh.pds.dao.enterprise.EnterpriseVerificationMapper;
import com.jh.pds.service.enterprise.EnterpriseVerificationService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 企业基本信息核验Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
@Transactional(readOnly = true)
public class EnterpriseVerificationServiceImpl extends BaseServiceImpl<EnterpriseVerificationMapper, EnterpriseVerification> implements EnterpriseVerificationService {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseVerificationServiceImpl.class);
    @Autowired
    private EnterpriseVerificationMapper enterpriseVerificationMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新企业基本信息核验
     *@param enterpriseVerification 企业基本信息核验对象
     *@return String 企业基本信息核验ID
     *<AUTHOR>
     */
    public String saveOrUpdateEnterpriseVerification(EnterpriseVerification enterpriseVerification) {
        if (enterpriseVerification == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(enterpriseVerification.getId())) {
            //新增
            enterpriseVerification.setId(UUIDUtils.getUUID());
            enterpriseVerificationMapper.insertSelective(enterpriseVerification);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            enterpriseVerification.setYn(null);
            enterpriseVerificationMapper.updateByPrimaryKeySelective(enterpriseVerification);
        }
        return enterpriseVerification.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除企业基本信息核验
     *@param ids void 企业基本信息核验ID
     *<AUTHOR>
     */
    public void deleteEnterpriseVerification(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            EnterpriseVerification enterpriseVerification = enterpriseVerificationMapper.selectByPrimaryKey(id);
            if (enterpriseVerification == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            EnterpriseVerification tementerpriseVerification = new EnterpriseVerification();
            tementerpriseVerification.setYn(CommonConstant.FLAG_NO);
            tementerpriseVerification.setId(enterpriseVerification.getId());
            enterpriseVerificationMapper.updateByPrimaryKeySelective(tementerpriseVerification);
        }
    }

    /**
     * 查询企业基本信息核验详情
     *
     * @param id
     * @return EnterpriseVerification
     * <AUTHOR>
     */
    @Override
    public EnterpriseVerification findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return enterpriseVerificationMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询企业基本信息核验
     *
     * @param enterpriseVerificationVo
     * @return PageInfo<EnterpriseVerification>
     * <AUTHOR>
     */
    @Override
    public PageInfo<EnterpriseVerification> findPageByQuery(EnterpriseVerificationVo enterpriseVerificationVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(enterpriseVerificationVo.getPageNum(), enterpriseVerificationVo.getPageSize());
        orderBy(enterpriseVerificationVo.getOrderColumn() + " " + enterpriseVerificationVo.getOrderValue());
        Example example = getExample(enterpriseVerificationVo);
        List<EnterpriseVerification> enterpriseVerificationList = enterpriseVerificationMapper.selectByExample(example);
        return new PageInfo<EnterpriseVerification>(enterpriseVerificationList);
    }

    private Example getExample(EnterpriseVerificationVo enterpriseVerificationVo) {
        Example example = new Example(EnterpriseVerification.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(enterpriseVerificationVo.getName())){
        //	criteria.andEqualTo(enterpriseVerificationVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

    /**
     * 校验企业信息字段
     *
     * @param fieldType 字段类型
     * @param fieldValue 字段值
     * @param additionalParam 附加参数
     * @return Map<String, Object> 校验结果
     * <AUTHOR>
     */
    @Override
    public Map<String, Object> verifyField(String fieldType, String fieldValue, String additionalParam) {
        Map<String, Object> result = new HashMap<>();
        result.put("fieldType", fieldType);
        result.put("inputValue", fieldValue);

        try {
            switch (fieldType) {
                // 基本信息校验
                case "enterprise_name":
                    boolean nameExists = enterpriseVerificationMapper.checkEnterpriseName(fieldValue);
                    result.put("verifyResult", nameExists ? "一致" : "不一致");
                    break;

                case "uscc":
                    boolean usccExists = enterpriseVerificationMapper.checkUSCC(fieldValue);
                    result.put("verifyResult", usccExists ? "一致" : "不一致");
                    break;

                case "legal_representative":
                    boolean legalExists = enterpriseVerificationMapper.checkLegalRepresentative(fieldValue, additionalParam);
                    result.put("verifyResult", legalExists ? "一致" : "不一致");
                    break;

                // 食品生产许可证校验
                case "food_issuing_authority":
                    boolean foodAuthorityExists = enterpriseVerificationMapper.checkFoodIssuingAuthority(fieldValue);
                    result.put("verifyResult", foodAuthorityExists ? "一致" : "不一致");
                    break;

                case "food_license_number":
                    boolean foodLicenseExists = enterpriseVerificationMapper.checkFoodLicenseNumber(fieldValue);
                    result.put("verifyResult", foodLicenseExists ? "一致" : "不一致");
                    break;

                case "food_issue_date":
                    boolean foodDateExists = enterpriseVerificationMapper.checkFoodIssueDate(fieldValue, additionalParam);
                    result.put("verifyResult", foodDateExists ? "一致" : "不一致");
                    break;

                case "food_certificate_status":
                    String foodStatus = enterpriseVerificationMapper.getFoodCertificateStatus(additionalParam);
                    result.put("verifyResult", "有效".equals(foodStatus) ? "未过期" : "过期");
                    break;

                // 全国工业产品生产许可证校验
                case "industrial_issue_date":
                    boolean industrialDateExists = enterpriseVerificationMapper.checkIndustrialIssueDate(fieldValue);
                    result.put("verifyResult", industrialDateExists ? "一致" : "不一致");
                    break;

                case "industrial_certificate_number":
                    boolean industrialNumberExists = enterpriseVerificationMapper.checkIndustrialCertificateNumber(fieldValue);
                    result.put("verifyResult", industrialNumberExists ? "一致" : "不一致");
                    break;

                case "industrial_certificate_status":
                    String industrialStatus = enterpriseVerificationMapper.getIndustrialCertificateStatus(additionalParam);
                    result.put("verifyResult", "有效".equals(industrialStatus) ? "未过期" : "过期");
                    break;

                // 专利授权信息校验
                case "patent_title":
                    boolean patentTitleExists = enterpriseVerificationMapper.checkPatentTitle(fieldValue);
                    result.put("verifyResult", patentTitleExists ? "一致" : "不一致");
                    break;

                case "patent_type":
                    boolean patentTypeExists = enterpriseVerificationMapper.checkPatentType(fieldValue, additionalParam);
                    result.put("verifyResult", patentTypeExists ? "一致" : "不一致");
                    break;

                case "applicant":
                    boolean applicantExists = enterpriseVerificationMapper.checkApplicant(fieldValue);
                    result.put("verifyResult", applicantExists ? "一致" : "不一致");
                    break;

                default:
                    result.put("verifyResult", "不支持的字段类型");
                    break;
            }
        } catch (Exception e) {
            logger.error("字段校验异常", e);
            result.put("verifyResult", "校验异常");
        }

        return result;
    }
}
