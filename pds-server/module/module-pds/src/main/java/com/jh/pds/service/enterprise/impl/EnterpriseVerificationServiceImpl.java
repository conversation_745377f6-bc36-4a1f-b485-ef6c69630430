package com.jh.pds.service.enterprise.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.pds.bean.enterprise.EnterpriseVerification;
import com.jh.pds.bean.enterprise.vo.EnterpriseVerificationVo;
import com.jh.pds.bean.enterprise.vo.FieldVerificationVo;
import com.jh.pds.dao.enterprise.EnterpriseVerificationMapper;
import com.jh.pds.enums.FieldVerificationEnum;
import com.jh.pds.service.enterprise.EnterpriseVerificationService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 企业基本信息核验Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
@Transactional(readOnly = true)
public class EnterpriseVerificationServiceImpl extends BaseServiceImpl<EnterpriseVerificationMapper, EnterpriseVerification> implements EnterpriseVerificationService {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseVerificationServiceImpl.class);
    @Autowired
    private EnterpriseVerificationMapper enterpriseVerificationMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新企业基本信息核验
     *@param enterpriseVerification 企业基本信息核验对象
     *@return String 企业基本信息核验ID
     *<AUTHOR>
     */
    public String saveOrUpdateEnterpriseVerification(EnterpriseVerification enterpriseVerification) {
        if (enterpriseVerification == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(enterpriseVerification.getId())) {
            //新增
            enterpriseVerification.setId(UUIDUtils.getUUID());
            enterpriseVerificationMapper.insertSelective(enterpriseVerification);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            enterpriseVerification.setYn(null);
            enterpriseVerificationMapper.updateByPrimaryKeySelective(enterpriseVerification);
        }
        return enterpriseVerification.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除企业基本信息核验
     *@param ids void 企业基本信息核验ID
     *<AUTHOR>
     */
    public void deleteEnterpriseVerification(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            EnterpriseVerification enterpriseVerification = enterpriseVerificationMapper.selectByPrimaryKey(id);
            if (enterpriseVerification == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            EnterpriseVerification tementerpriseVerification = new EnterpriseVerification();
            tementerpriseVerification.setYn(CommonConstant.FLAG_NO);
            tementerpriseVerification.setId(enterpriseVerification.getId());
            enterpriseVerificationMapper.updateByPrimaryKeySelective(tementerpriseVerification);
        }
    }

    /**
     * 查询企业基本信息核验详情
     *
     * @param id
     * @return EnterpriseVerification
     * <AUTHOR>
     */
    @Override
    public EnterpriseVerification findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return enterpriseVerificationMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询企业基本信息核验
     *
     * @param enterpriseVerificationVo
     * @return PageInfo<EnterpriseVerification>
     * <AUTHOR>
     */
    @Override
    public PageInfo<EnterpriseVerification> findPageByQuery(EnterpriseVerificationVo enterpriseVerificationVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(enterpriseVerificationVo.getPageNum(), enterpriseVerificationVo.getPageSize());
        orderBy(enterpriseVerificationVo.getOrderColumn() + " " + enterpriseVerificationVo.getOrderValue());
        Example example = getExample(enterpriseVerificationVo);
        List<EnterpriseVerification> enterpriseVerificationList = enterpriseVerificationMapper.selectByExample(example);
        return new PageInfo<EnterpriseVerification>(enterpriseVerificationList);
    }

    private Example getExample(EnterpriseVerificationVo enterpriseVerificationVo) {
        Example example = new Example(EnterpriseVerification.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(enterpriseVerificationVo.getName())){
        //	criteria.andEqualTo(enterpriseVerificationVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

    /**
     * 校验企业信息字段
     *
     * @param verificationVo 校验请求参数
     * @return Integer 校验结果：1表示一致，0表示不一致
     * <AUTHOR>
     */
    @Override
    public Integer verifyField(FieldVerificationVo verificationVo) {
        String fieldType = verificationVo.getFieldType();
        String fieldValue = verificationVo.getFieldValue();
        String additionalParam = verificationVo.getAdditionalParam();

        if (StringUtils.isEmpty(fieldType)) {
            logger.warn("字段类型为空: fieldType={}", fieldType);
            return 0;
        }

        // 获取字段配置枚举
        FieldVerificationEnum fieldEnum = FieldVerificationEnum.getByFieldType(fieldType);
        if (fieldEnum == null) {
            logger.warn("不支持的字段类型: {}", fieldType);
            return 0;
        }

        // 证书状态字段不需要校验fieldValue
        if (!fieldEnum.isCertificateStatus() && StringUtils.isEmpty(fieldValue)) {
            logger.warn("字段值为空: fieldType={}, fieldValue={}", fieldType, fieldValue);
            return 0;
        }

        // 检查是否需要附加参数
        if (fieldEnum.isNeedAdditionalParam() && StringUtils.isEmpty(additionalParam)) {
            logger.warn("缺少必需的附加参数: fieldType={}, needAdditionalParam={}", fieldType, fieldEnum.isNeedAdditionalParam());
            return 0;
        }

        try {
            // 处理证书状态字段
            if (fieldEnum.isCertificateStatus()) {
                String status;
                switch (fieldEnum) {
                    case FOOD_CERTIFICATE_STATUS:
                        status = enterpriseVerificationMapper.getFoodCertificateStatus(additionalParam);
                        break;
                    case INDUSTRIAL_CERTIFICATE_STATUS:
                        status = enterpriseVerificationMapper.getIndustrialCertificateStatus(additionalParam);
                        break;
                    default:
                        logger.warn("不支持的证书状态字段: {}", fieldEnum.getFieldType());
                        return 0;
                }
                return "有效".equals(status) ? 1 : 0;
            }

            // 处理普通字段校验
            boolean result;
            String additionalFieldName = fieldEnum.isNeedAdditionalParam() ? fieldEnum.getAdditionalFieldName() : null;

            switch (fieldEnum.getTable()) {
                case MARKET_ENTITY:
                    result = enterpriseVerificationMapper.checkMarketEntityInfo(
                        fieldEnum.getDbFieldName(), fieldValue, additionalFieldName, additionalParam);
                    break;
                case FOOD_PRODUCTION_LICENSE:
                    result = enterpriseVerificationMapper.checkFoodProductionLicense(
                        fieldEnum.getDbFieldName(), fieldValue, additionalFieldName, additionalParam);
                    break;
                case INDUSTRIAL_PRODUCT_LICENSE:
                    result = enterpriseVerificationMapper.checkIndustrialProductLicense(
                        fieldEnum.getDbFieldName(), fieldValue, additionalFieldName, additionalParam);
                    break;
                case PATENT_AUTHORIZATION:
                    result = enterpriseVerificationMapper.checkPatentAuthorizationInfo(
                        fieldEnum.getDbFieldName(), fieldValue, additionalFieldName, additionalParam);
                    break;
                default:
                    logger.warn("不支持的表类型: {}", fieldEnum.getTable());
                    return 0;
            }
            return result ? 1 : 0;

        } catch (Exception e) {
            logger.error("字段校验异常: fieldType={}, fieldValue={}", fieldType, fieldValue, e);
            return 0;
        }
    }
}
