package com.jh.pds.controller.enterprise;

import java.util.List;
import com.jh.pds.bean.enterprise.EnterpriseVerification;
import com.jh.pds.bean.enterprise.vo.EnterpriseVerificationVo;
import com.jh.pds.bean.enterprise.vo.FieldVerificationVo;
import com.jh.pds.service.enterprise.EnterpriseVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 企业基本信息核验Controller
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/enterprise/verification")
@Tag(name = "企业基本信息核验")
public class EnterpriseVerificationController extends BaseController {
    @Autowired
    private EnterpriseVerificationService enterpriseVerificationService;

    private static final String PER_PREFIX = "btn:enterprise:verification:";


    /**
     * 批量删除企业基本信息核验(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除企业基本信息核验")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "批量删除企业基本信息核验")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteEnterpriseVerification(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        enterpriseVerificationService.deleteEnterpriseVerification(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询企业基本信息核验详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询企业基本信息核验详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        EnterpriseVerification enterpriseVerification = enterpriseVerificationService.findById(id);
        return RestApiResponse.ok(enterpriseVerification);
    }

    /**
     * 分页查询企业基本信息核验
     *
     * @param enterpriseVerificationVo 企业基本信息核验 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询企业基本信息核验")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody EnterpriseVerificationVo enterpriseVerificationVo) {
        PageInfo<EnterpriseVerification> enterpriseVerification = enterpriseVerificationService.findPageByQuery(enterpriseVerificationVo);
        return RestApiResponse.ok(enterpriseVerification);
    }

    /**
     * 校验企业信息字段
     *
     * @param verificationVo 校验请求参数
     * @return RestApiResponse<Integer> 返回校验结果：1表示一致，0表示不一致
     * <AUTHOR>
     */
    @PostMapping("/verifyField")
    @Operation(summary = "校验企业信息字段")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "校验企业信息字段")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "verify')")
    public RestApiResponse<Integer> verifyField(@RequestBody FieldVerificationVo verificationVo) {
        Integer result = enterpriseVerificationService.verifyField(verificationVo);
        return RestApiResponse.ok(result);
    }

    /**
     * 保存企业信息核验记录（包含核验结果）
     *
     * @param verificationVo 保存请求参数
     * @return RestApiResponse<String> 返回保存的记录ID
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/saveWithResults")
    @Operation(summary = "保存企业信息核验记录（包含核验结果）")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "保存企业信息核验记录")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<String> saveEnterpriseVerificationWithResults(@RequestBody EnterpriseVerificationVo verificationVo) {
        String id = enterpriseVerificationService.saveEnterpriseVerificationWithResults(verificationVo);
        return RestApiResponse.ok(id);
    }

}
