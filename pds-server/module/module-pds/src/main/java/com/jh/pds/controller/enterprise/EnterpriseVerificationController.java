package com.jh.pds.controller.enterprise;

import java.util.List;
import java.util.Map;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.pds.bean.enterprise.EnterpriseVerification;
import com.jh.pds.bean.enterprise.vo.EnterpriseVerificationVo;
import com.jh.pds.service.enterprise.EnterpriseVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 企业基本信息核验Controller
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/jh/verification")
@Tag(name = "企业基本信息核验")
public class EnterpriseVerificationController extends BaseController {
    @Autowired
    private EnterpriseVerificationService enterpriseVerificationService;

    private static final String PER_PREFIX = "btn:jh:verification:";

    /**
     * 新增企业基本信息核验
     *
     * @param enterpriseVerification 企业基本信息核验数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增企业基本信息核验")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "新增企业基本信息核验")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveEnterpriseVerification(@RequestBody EnterpriseVerification enterpriseVerification) {
        String id = enterpriseVerificationService.saveOrUpdateEnterpriseVerification(enterpriseVerification);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改企业基本信息核验
     *
     * @param enterpriseVerification 企业基本信息核验数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改企业基本信息核验")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "修改企业基本信息核验")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateEnterpriseVerification(@RequestBody EnterpriseVerification enterpriseVerification) {
        String id = enterpriseVerificationService.saveOrUpdateEnterpriseVerification(enterpriseVerification);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除企业基本信息核验(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除企业基本信息核验")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "批量删除企业基本信息核验")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteEnterpriseVerification(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        enterpriseVerificationService.deleteEnterpriseVerification(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询企业基本信息核验详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询企业基本信息核验详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        EnterpriseVerification enterpriseVerification = enterpriseVerificationService.findById(id);
        return RestApiResponse.ok(enterpriseVerification);
    }

    /**
     * 分页查询企业基本信息核验
     *
     * @param enterpriseVerificationVo 企业基本信息核验 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询企业基本信息核验")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody EnterpriseVerificationVo enterpriseVerificationVo) {
        PageInfo<EnterpriseVerification> enterpriseVerification = enterpriseVerificationService.findPageByQuery(enterpriseVerificationVo);
        return RestApiResponse.ok(enterpriseVerification);
    }

    /**
     * 校验企业信息字段
     *
     * @param fieldType 字段类型（enterprise_name, uscc, legal_representative, food_issuing_authority等）
     * @param fieldValue 字段值
     * @param additionalParam 附加参数（如统一社会信用代码，用于关联查询）
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/verifyField")
    @Operation(summary = "校验企业信息字段")
    @SystemLogAnnotation(type = "企业基本信息核验", value = "校验企业信息字段")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "verify')")
    public RestApiResponse<?> verifyField(@RequestParam("fieldType") String fieldType,
                                          @RequestParam("fieldValue") String fieldValue,
                                          @RequestParam(value = "additionalParam", required = false) String additionalParam) {
        Map<String, Object> result = enterpriseVerificationService.verifyField(fieldType, fieldValue, additionalParam);
        return RestApiResponse.ok(result);
    }

}
