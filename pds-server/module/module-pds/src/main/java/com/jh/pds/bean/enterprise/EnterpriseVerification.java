package com.jh.pds.bean.enterprise;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 企业基本信息核验对象 enterprise_verification
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Table(name = "enterprise_verification")
@Schema(description = "企业基本信息核验")
@Data
public class EnterpriseVerification extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;

    @Column(name = "ENTERPRISE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    private String enterpriseName;

    @Column(name = "USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "统一社会信用代码")
    private String uscc;

    @Column(name = "LEGAL_REPRESENTATIVE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "法定代表人")
    private String legalRepresentative;

    // 食品生产许可证相关字段
    @Column(name = "FOOD_ISSUING_AUTHORITY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "食品生产许可证证面信息-发证机关")
    private String foodIssuingAuthority;

    @Column(name = "FOOD_LICENSE_NUMBER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "食品生产许可证证面信息-许可证编号")
    private String foodLicenseNumber;

    @Column(name = "FOOD_ISSUE_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "食品生产许可证证面信息-发证日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date foodIssueDate;

    @Column(name = "FOOD_CERTIFICATE_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "食品生产许可证证面信息-证书状态")
    private String foodCertificateStatus;

    // 全国工业产品生产许可证相关字段
    @Column(name = "INDUSTRIAL_ISSUE_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "全国工业产品生产许可证-发证日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date industrialIssueDate;

    @Column(name = "INDUSTRIAL_CERTIFICATE_NUMBER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "全国工业产品生产许可证-证书编号")
    private String industrialCertificateNumber;

    @Column(name = "INDUSTRIAL_CERTIFICATE_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "全国工业产品生产许可证-证书状态")
    private String industrialCertificateStatus;

    // 专利授权信息相关字段
    @Column(name = "PATENT_TITLE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "专利标题")
    private String patentTitle;

    @Column(name = "PATENT_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "专利类型")
    private String patentType;

    @Column(name = "APPLICANT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "申请人")
    private String applicant;

    // 基本信息核验结果
    @Column(name = "ENTERPRISE_NAME_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "企业名称核验结果：1-一致，0-不一致")
    private Integer enterpriseNameVerifyResult;

    @Column(name = "USCC_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "统一社会信用代码核验结果：1-一致，0-不一致")
    private Integer usccVerifyResult;

    @Column(name = "LEGAL_REPRESENTATIVE_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "法定代表人核验结果：1-一致，0-不一致")
    private Integer legalRepresentativeVerifyResult;

    // 食品生产许可证核验结果
    @Column(name = "FOOD_ISSUING_AUTHORITY_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "食品生产许可证发证机关核验结果：1-一致，0-不一致")
    private Integer foodIssuingAuthorityVerifyResult;

    @Column(name = "FOOD_LICENSE_NUMBER_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "食品生产许可证编号核验结果：1-一致，0-不一致")
    private Integer foodLicenseNumberVerifyResult;

    @Column(name = "FOOD_ISSUE_DATE_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "食品生产许可证发证日期核验结果：1-一致，0-不一致")
    private Integer foodIssueDateVerifyResult;

    @Column(name = "FOOD_CERTIFICATE_STATUS_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "食品生产许可证状态核验结果：1-有效，0-无效")
    private Integer foodCertificateStatusVerifyResult;

    // 全国工业产品生产许可证核验结果
    @Column(name = "INDUSTRIAL_ISSUE_DATE_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "工业产品许可证发证日期核验结果：1-一致，0-不一致")
    private Integer industrialIssueDateVerifyResult;

    @Column(name = "INDUSTRIAL_CERTIFICATE_NUMBER_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "工业产品许可证编号核验结果：1-一致，0-不一致")
    private Integer industrialCertificateNumberVerifyResult;

    @Column(name = "INDUSTRIAL_CERTIFICATE_STATUS_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "工业产品许可证状态核验结果：1-有效，0-无效")
    private Integer industrialCertificateStatusVerifyResult;

    // 专利授权信息核验结果
    @Column(name = "PATENT_TITLE_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "专利标题核验结果：1-一致，0-不一致")
    private Integer patentTitleVerifyResult;

    @Column(name = "PATENT_TYPE_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "专利类型核验结果：1-一致，0-不一致")
    private Integer patentTypeVerifyResult;

    @Column(name = "APPLICANT_VERIFY_RESULT")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "申请人核验结果：1-一致，0-不一致")
    private Integer applicantVerifyResult;

    // 核验汇总信息
    @Column(name = "VERIFY_STATUS")
    @ColumnType(jdbcType = JdbcType.TINYINT)
    @Schema(description = "整体核验状态：0-未完成，1-已完成")
    private Integer verifyStatus;

    @Column(name = "VERIFY_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "核验完成时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;
}