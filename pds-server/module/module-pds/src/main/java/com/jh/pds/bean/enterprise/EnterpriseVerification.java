package com.jh.pds.bean.enterprise;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 企业基本信息核验对象 enterprise_verification
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Table(name = "enterprise_verification")
@Schema(description = "企业基本信息核验")
@Data
public class EnterpriseVerification extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "用户ID")
    private String userId;

    @Column(name = "ENTERPRISE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    private String enterpriseName;

    @Column(name = "USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "统一社会信用代码")
    private String uscc;

    @Column(name = "LEGAL_REPRESENTATIVE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "法定代表人")
    private String legalRepresentative;

    // 食品生产许可证相关字段
    @Column(name = "FOOD_ISSUING_AUTHORITY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "食品生产许可证证面信息-发证机关")
    private String foodIssuingAuthority;

    @Column(name = "FOOD_LICENSE_NUMBER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "食品生产许可证证面信息-许可证编号")
    private String foodLicenseNumber;

    @Column(name = "FOOD_ISSUE_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "食品生产许可证证面信息-发证日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date foodIssueDate;

    @Column(name = "FOOD_CERTIFICATE_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "食品生产许可证证面信息-证书状态")
    private String foodCertificateStatus;

    // 全国工业产品生产许可证相关字段
    @Column(name = "INDUSTRIAL_ISSUE_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "全国工业产品生产许可证-发证日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date industrialIssueDate;

    @Column(name = "INDUSTRIAL_CERTIFICATE_NUMBER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "全国工业产品生产许可证-证书编号")
    private String industrialCertificateNumber;

    @Column(name = "INDUSTRIAL_CERTIFICATE_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "全国工业产品生产许可证-证书状态")
    private String industrialCertificateStatus;

    // 专利授权信息相关字段
    @Column(name = "PATENT_TITLE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "专利标题")
    private String patentTitle;

    @Column(name = "PATENT_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "专利类型")
    private String patentType;

    @Column(name = "APPLICANT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "申请人")
    private String applicant;
}