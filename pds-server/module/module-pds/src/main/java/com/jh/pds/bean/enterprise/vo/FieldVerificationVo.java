package com.jh.pds.bean.enterprise.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 字段校验请求VO
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Schema(description = "字段校验请求VO")
@Data
public class FieldVerificationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字段类型
     * 支持的字段类型：
     * 基本信息：enterprise_name, uscc, legal_representative
     * 食品生产许可证：food_issuing_authority, food_license_number, food_issue_date, food_certificate_status
     * 全国工业产品生产许可证：industrial_issue_date, industrial_certificate_number, industrial_certificate_status
     * 专利授权信息：patent_title, patent_type, applicant
     */
    @Schema(description = "字段类型", required = true, example = "enterprise_name")
    private String fieldType;

    /**
     * 字段值
     */
    @Schema(description = "字段值", required = true, example = "北京科技有限公司")
    private String fieldValue;

    /**
     * 附加参数（如统一社会信用代码，用于关联查询）
     * 某些字段校验需要额外的关联参数：
     * - legal_representative 需要 uscc（统一社会信用代码）
     * - food_issue_date 需要 licenseNumber（许可证编号）
     * - food_certificate_status 需要 licenseNumber（许可证编号）
     * - patent_type 需要 patentTitle（专利标题）
     * - industrial_certificate_status 需要 certificateNumber（证书编号）
     */
    @Schema(description = "附加参数", example = "91110000123456789X")
    private String additionalParam;

}
