package com.jh.pds.dao.enterprise;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.pds.bean.enterprise.EnterpriseVerification;
import org.apache.ibatis.annotations.Param;

/**
 * 企业基本信息核验Mapper接口
 *<AUTHOR>
 *@date 2025-08-01
 */
public interface EnterpriseVerificationMapper extends BaseInfoMapper<EnterpriseVerification>{

    // 基本信息校验
    boolean checkEnterpriseName(@Param("enterpriseName") String enterpriseName);
    boolean checkUSCC(@Param("uscc") String uscc);
    boolean checkLegalRepresentative(@Param("legalRepresentative") String legalRepresentative, @Param("uscc") String uscc);

    // 食品生产许可证校验
    boolean checkFoodIssuingAuthority(@Param("issuingAuthority") String issuingAuthority);
    boolean checkFoodLicenseNumber(@Param("licenseNumber") String licenseNumber);
    boolean checkFoodIssueDate(@Param("issueDate") String issueDate, @Param("licenseNumber") String licenseNumber);
    String getFoodCertificateStatus(@Param("licenseNumber") String licenseNumber);

    // 全国工业产品生产许可证校验
    boolean checkIndustrialIssueDate(@Param("issueDate") String issueDate);
    boolean checkIndustrialCertificateNumber(@Param("certificateNumber") String certificateNumber);
    String getIndustrialCertificateStatus(@Param("certificateNumber") String certificateNumber);

    // 专利授权信息校验
    boolean checkPatentTitle(@Param("patentTitle") String patentTitle);
    boolean checkPatentType(@Param("patentType") String patentType, @Param("patentTitle") String patentTitle);
    boolean checkApplicant(@Param("applicant") String applicant);
}