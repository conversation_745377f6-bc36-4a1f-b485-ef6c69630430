package com.jh.pds.dao.enterprise;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.pds.bean.enterprise.EnterpriseVerification;
import org.apache.ibatis.annotations.Param;

/**
 * 企业基本信息核验Mapper接口
 *<AUTHOR>
 *@date 2025-08-01
 */
public interface EnterpriseVerificationMapper extends BaseInfoMapper<EnterpriseVerification>{

    /**
     * 校验市场主体信息
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param additionalField 附加字段名（可选）
     * @param additionalValue 附加字段值（可选）
     * @return 是否存在匹配记录
     */
    boolean checkMarketEntityInfo(@Param("fieldName") String fieldName,
                                  @Param("fieldValue") String fieldValue,
                                  @Param("additionalField") String additionalField,
                                  @Param("additionalValue") String additionalValue);

    /**
     * 校验食品生产许可证信息
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param additionalField 附加字段名（可选）
     * @param additionalValue 附加字段值（可选）
     * @return 是否存在匹配记录
     */
    boolean checkFoodProductionLicense(@Param("fieldName") String fieldName,
                                       @Param("fieldValue") String fieldValue,
                                       @Param("additionalField") String additionalField,
                                       @Param("additionalValue") String additionalValue);

    /**
     * 获取食品生产许可证状态
     * @param licenseNumber 许可证编号
     * @return 证书状态
     */
    String getFoodCertificateStatus(@Param("licenseNumber") String licenseNumber);

    /**
     * 校验全国工业产品生产许可证信息
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param additionalField 附加字段名（可选）
     * @param additionalValue 附加字段值（可选）
     * @return 是否存在匹配记录
     */
    boolean checkIndustrialProductLicense(@Param("fieldName") String fieldName,
                                          @Param("fieldValue") String fieldValue,
                                          @Param("additionalField") String additionalField,
                                          @Param("additionalValue") String additionalValue);

    /**
     * 获取全国工业产品生产许可证状态
     * @param certificateNumber 证书编号
     * @return 证书状态
     */
    String getIndustrialCertificateStatus(@Param("certificateNumber") String certificateNumber);

    /**
     * 校验专利授权信息
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param additionalField 附加字段名（可选）
     * @param additionalValue 附加字段值（可选）
     * @return 是否存在匹配记录
     */
    boolean checkPatentAuthorizationInfo(@Param("fieldName") String fieldName,
                                         @Param("fieldValue") String fieldValue,
                                         @Param("additionalField") String additionalField,
                                         @Param("additionalValue") String additionalValue);
}