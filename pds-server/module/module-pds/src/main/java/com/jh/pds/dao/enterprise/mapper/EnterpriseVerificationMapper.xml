<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.pds.dao.enterprise.EnterpriseVerificationMapper">

    <!-- 基本信息校验 -->
    <select id="checkEnterpriseName" resultType="boolean">
        SELECT COUNT(1) > 0 FROM market_entity_info
        WHERE ENTERPRISE_NAME = #{enterpriseName} AND YN = 1
    </select>

    <select id="checkUSCC" resultType="boolean">
        SELECT COUNT(1) > 0 FROM market_entity_info
        WHERE UNIFIED_SOCIAL_CREDIT_CODE = #{uscc} AND YN = 1
    </select>

    <select id="checkLegalRepresentative" resultType="boolean">
        SELECT COUNT(1) > 0 FROM market_entity_info
        WHERE LEGAL_REPRESENTATIVE = #{legalRepresentative}
        <if test="uscc != null and uscc != ''">
            AND UNIFIED_SOCIAL_CREDIT_CODE = #{uscc}
        </if>
        AND YN = 1
    </select>

    <!-- 食品生产许可证校验 -->
    <select id="checkFoodIssuingAuthority" resultType="boolean">
        SELECT COUNT(1) > 0 FROM food_production_license
        WHERE ISSUING_AUTHORITY = #{issuingAuthority} AND YN = 1
    </select>

    <select id="checkFoodLicenseNumber" resultType="boolean">
        SELECT COUNT(1) > 0 FROM food_production_license
        WHERE LICENSE_NUMBER = #{licenseNumber} AND YN = 1
    </select>

    <select id="checkFoodIssueDate" resultType="boolean">
        SELECT COUNT(1) > 0 FROM food_production_license
        WHERE ISSUE_DATE = #{issueDate}
        <if test="licenseNumber != null and licenseNumber != ''">
            AND LICENSE_NUMBER = #{licenseNumber}
        </if>
        AND YN = 1
    </select>

    <select id="getFoodCertificateStatus" resultType="string">
        SELECT CERTIFICATE_STATUS FROM food_production_license
        WHERE LICENSE_NUMBER = #{licenseNumber} AND YN = 1
            LIMIT 1
    </select>

    <!-- 全国工业产品生产许可证校验 -->
    <select id="checkIndustrialIssueDate" resultType="boolean">
        SELECT COUNT(1) > 0 FROM industrial_product_license
        WHERE ISSUE_DATE = #{issueDate} AND YN = 1
    </select>

    <select id="checkIndustrialCertificateNumber" resultType="boolean">
        SELECT COUNT(1) > 0 FROM industrial_product_license
        WHERE CERTIFICATE_NUMBER = #{certificateNumber} AND YN = 1
    </select>

    <select id="getIndustrialCertificateStatus" resultType="string">
        SELECT CERTIFICATE_STATUS FROM industrial_product_license
        WHERE CERTIFICATE_NUMBER = #{certificateNumber} AND YN = 1
            LIMIT 1
    </select>

    <!-- 专利授权信息校验 -->
    <select id="checkPatentTitle" resultType="boolean">
        SELECT COUNT(1) > 0 FROM patent_authorization_info
        WHERE PATENT_TITLE = #{patentTitle} AND YN = 1
    </select>

    <select id="checkPatentType" resultType="boolean">
        SELECT COUNT(1) > 0 FROM patent_authorization_info
        WHERE PATENT_TYPE = #{patentType}
        <if test="patentTitle != null and patentTitle != ''">
            AND PATENT_TITLE = #{patentTitle}
        </if>
        AND YN = 1
    </select>

    <select id="checkApplicant" resultType="boolean">
        SELECT COUNT(1) > 0 FROM patent_authorization_info
        WHERE APPLICANT = #{applicant} AND YN = 1
    </select>

</mapper>