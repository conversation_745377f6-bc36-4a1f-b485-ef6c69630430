<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.pds.dao.enterprise.EnterpriseVerificationMapper">

    <!-- 市场主体信息校验 -->
    <select id="checkMarketEntityInfo" resultType="boolean">
        SELECT COUNT(1) > 0 FROM market_entity_info
        WHERE 1=1
        <if test="fieldName == 'ENTERPRISE_NAME'">
            AND ENTERPRISE_NAME = #{fieldValue}
        </if>
        <if test="fieldName == 'UNIFIED_SOCIAL_CREDIT_CODE'">
            AND UNIFIED_SOCIAL_CREDIT_CODE = #{fieldValue}
        </if>
        <if test="fieldName == 'LEGAL_REPRESENTATIVE'">
            AND LEGAL_REPRESENTATIVE = #{fieldValue}
            <if test="additionalField == 'UNIFIED_SOCIAL_CREDIT_CODE' and additionalValue != null and additionalValue != ''">
                AND UNIFIED_SOCIAL_CREDIT_CODE = #{additionalValue}
            </if>
        </if>
        AND YN = 1
    </select>

    <!-- 食品生产许可证校验 -->
    <select id="checkFoodProductionLicense" resultType="boolean">
        SELECT COUNT(1) > 0 FROM food_production_license
        WHERE 1=1
        <if test="fieldName == 'ISSUING_AUTHORITY'">
            AND ISSUING_AUTHORITY = #{fieldValue}
        </if>
        <if test="fieldName == 'LICENSE_NUMBER'">
            AND LICENSE_NUMBER = #{fieldValue}
        </if>
        <if test="fieldName == 'ISSUE_DATE'">
            AND ISSUE_DATE = #{fieldValue}
            <if test="additionalField == 'LICENSE_NUMBER' and additionalValue != null and additionalValue != ''">
                AND LICENSE_NUMBER = #{additionalValue}
            </if>
        </if>
        AND YN = 1
    </select>

    <select id="getFoodCertificateStatus" resultType="string">
        SELECT CERTIFICATE_STATUS FROM food_production_license
        WHERE LICENSE_NUMBER = #{licenseNumber} AND YN = 1
        LIMIT 1
    </select>

    <!-- 全国工业产品生产许可证校验 -->
    <select id="checkIndustrialProductLicense" resultType="boolean">
        SELECT COUNT(1) > 0 FROM industrial_product_license
        WHERE 1=1
        <if test="fieldName == 'ISSUE_DATE'">
            AND ISSUE_DATE = #{fieldValue}
        </if>
        <if test="fieldName == 'CERTIFICATE_NUMBER'">
            AND CERTIFICATE_NUMBER = #{fieldValue}
        </if>
        AND YN = 1
    </select>

    <select id="getIndustrialCertificateStatus" resultType="string">
        SELECT CERTIFICATE_STATUS FROM industrial_product_license
        WHERE CERTIFICATE_NUMBER = #{certificateNumber} AND YN = 1
        LIMIT 1
    </select>

    <!-- 专利授权信息校验 -->
    <select id="checkPatentAuthorizationInfo" resultType="boolean">
        SELECT COUNT(1) > 0 FROM patent_authorization_info
        WHERE 1=1
        <if test="fieldName == 'PATENT_TITLE'">
            AND PATENT_TITLE = #{fieldValue}
        </if>
        <if test="fieldName == 'PATENT_TYPE'">
            AND PATENT_TYPE = #{fieldValue}
            <if test="additionalField == 'PATENT_TITLE' and additionalValue != null and additionalValue != ''">
                AND PATENT_TITLE = #{additionalValue}
            </if>
        </if>
        <if test="fieldName == 'APPLICANT'">
            AND APPLICANT = #{fieldValue}
        </if>
        AND YN = 1
    </select>

</mapper>