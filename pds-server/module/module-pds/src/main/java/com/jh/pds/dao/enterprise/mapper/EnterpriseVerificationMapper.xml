<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.pds.dao.enterprise.EnterpriseVerificationMapper">

    <!-- 市场主体信息校验 -->
    <select id="checkMarketEntityInfo" resultType="boolean">
        SELECT COUNT(1) > 0 FROM market_entity_info
        WHERE ${fieldName} = #{fieldValue}
        <if test="additionalField != null and additionalField != '' and additionalValue != null and additionalValue != ''">
            AND ${additionalField} = #{additionalValue}
        </if>
        AND YN = 1
    </select>

    <!-- 食品生产许可证校验 -->
    <select id="checkFoodProductionLicense" resultType="boolean">
        SELECT COUNT(1) > 0 FROM food_production_license
        WHERE ${fieldName} = #{fieldValue}
        <if test="additionalField != null and additionalField != '' and additionalValue != null and additionalValue != ''">
            AND ${additionalField} = #{additionalValue}
        </if>
        AND YN = 1
    </select>

    <select id="getFoodCertificateStatus" resultType="string">
        SELECT CERTIFICATE_STATUS FROM food_production_license
        WHERE LICENSE_NUMBER = #{licenseNumber} AND YN = 1
        LIMIT 1
    </select>

    <!-- 全国工业产品生产许可证校验 -->
    <select id="checkIndustrialProductLicense" resultType="boolean">
        SELECT COUNT(1) > 0 FROM industrial_product_license
        WHERE ${fieldName} = #{fieldValue}
        <if test="additionalField != null and additionalField != '' and additionalValue != null and additionalValue != ''">
            AND ${additionalField} = #{additionalValue}
        </if>
        AND YN = 1
    </select>

    <select id="getIndustrialCertificateStatus" resultType="string">
        SELECT CERTIFICATE_STATUS FROM industrial_product_license
        WHERE CERTIFICATE_NUMBER = #{certificateNumber} AND YN = 1
        LIMIT 1
    </select>

    <!-- 专利授权信息校验 -->
    <select id="checkPatentAuthorizationInfo" resultType="boolean">
        SELECT COUNT(1) > 0 FROM patent_authorization_info
        WHERE ${fieldName} = #{fieldValue}
        <if test="additionalField != null and additionalField != '' and additionalValue != null and additionalValue != ''">
            AND ${additionalField} = #{additionalValue}
        </if>
        AND YN = 1
    </select>

</mapper>