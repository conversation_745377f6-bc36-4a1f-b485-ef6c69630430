package ${packageName}.${moduleName}.service;

import java.util.List;
import com.github.pagehelper.PageInfo;
import ${packageName}.${moduleName}.bean.${ClassName};
import ${packageName}.${moduleName}.bean.vo.${ClassName}Vo;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
/**
 * ${functionName}Service接口
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
public interface ${ClassName}Service{
	/**
	 * 保存或更新${functionName}
	 *@param ${className} ${functionName}对象
	 *@return String ${functionName}ID
	 *<AUTHOR>
	 */
	String saveOrUpdate${ClassName}(${ClassName} ${className});
	
	/**
	 * 删除${functionName}
	 *@param ids void ${functionName}ID
	 *<AUTHOR>
	 */
	void delete${ClassName}(List<String> ids);

	/**
	 * 查询${functionName}详情
	 *@param id
	 *@return ${ClassName}
	 *<AUTHOR>
	 */
	${ClassName} findById(String id);

	/**
	 * 分页查询${functionName}
	 *@param ${className}Vo
	 *@return PageInfo<${ClassName}>
	 *<AUTHOR>
	 */
	PageInfo<${ClassName}> findPageByQuery(${ClassName}Vo ${className}Vo);

	/**
     * 按条件导出查询${functionName}
     *@param ${className}Vo
     *@return PageInfo<${ClassName}>
     *<AUTHOR>
     */
    List<${ClassName}> findByQuery(${ClassName}Vo ${className}Vo);

    /**
     * 导入${functionName}
     *@param file
     *@param cover 是否覆盖 1 覆盖 0 不覆盖
     *@return
     *<AUTHOR>
     */
    void import${ClassName}Async(MultipartFile file, Integer cover);
}
