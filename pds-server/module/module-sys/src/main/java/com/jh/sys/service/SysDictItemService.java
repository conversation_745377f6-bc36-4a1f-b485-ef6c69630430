package com.jh.sys.service;

import com.jh.sys.bean.SysDictItem;
import com.jh.sys.bean.vo.DictTree;
import com.jh.sys.bean.vo.SysDictVo;

import java.util.List;

/**
 * 系统数据字典分类
 *
 * <AUTHOR>
 * @date 2020-07-05 15:14:51
 */
public interface SysDictItemService {

    /**
     * 根据userId查询字典（新）
     *
     * @param dictItem 字典对象
     * @return List<BasicDictItem>
     */
    List<SysDictItem> listItem(SysDictItem dictItem, boolean isAdmin);

    /**
     * 新增系统数据字典分类
     *
     * @param basicDictItem 字典对象
     */
    void addBasicDictItem(SysDictItem basicDictItem, SysDictVo dictVo);

    /**
     * 更新系统数据字典（新）
     *
     * @param basicDictItem 字典对象
     * @param isAdmin       是否是管理员
     */
    void updateDictItem(SysDictItem basicDictItem, boolean isAdmin);

    /**
     * 删除系统数据字典分类
     *
     * @param id void 系统数据字典分类ID
     */
    void deleteById(String id);

    /**
     * 根据字典code得到字典的值
     *
     * @param code 字典编码
     * @return 字典信息
     */
    SysDictItem findDictByCode(String code);

    /**
     * 根据字典CODE得到下级结点列表一级
     *
     * @param typeCode 字典编码
     * @return 字典集合
     */
    List<SysDictItem> findDictItemByDictCode(String typeCode);

    /**
     * 查询系统数据字典分类详情
     *
     * @param id 字典ID
     * @return BasicDictItem 字典信息
     */
    SysDictItem findById(String id);

    /**
     * 获取字典树
     *
     * @param admin 是否是管理员
     * @return 结果
     */
    List<DictTree> getDictTree(boolean admin);
}
