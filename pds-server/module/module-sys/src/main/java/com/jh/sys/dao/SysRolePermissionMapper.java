package com.jh.sys.dao;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.SysRolePermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;
public interface SysRolePermissionMapper extends BaseInfoMapper<SysRolePermission> {

    void deletePermissionByRoleCode(@Param("roleCode") String userCode,
                                           @Param("resourceType") String resourceType, @Param("menuId") String menuId);

    /**
     * @param roleIds
     * @Description:根据角色id查询所有的目录权限Code
     * @param:
     * @return:
     * @throws:
     * <AUTHOR>
     */
    List<String> findMenuPermissionCodeByRoleId(@Param("roleIds") List<String> roleIds);

    List<String> findElementPermissionCodeByRoleId(@Param("roleIds") List<String> roleIds);
}