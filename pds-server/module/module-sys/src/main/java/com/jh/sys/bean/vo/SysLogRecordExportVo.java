package com.jh.sys.bean.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/07/15
 */
public class SysLogRecordExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("用户名")
    private String userName;

    @ExcelProperty("模块")
    private String module;

    @ExcelProperty("时间")
    private Date createTime;

    @ExcelProperty("状态")
    private String flag;

    @ExcelProperty("IP")
    private String ip;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("方法参数")
    private String params;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Date getCreateTime() {
        if (createTime != null) {
            return new Date(createTime.getTime());
        }
        return null;
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        }
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag == 1 ? "成功" : "失败";
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

}
