package com.jh.sys.dao;

import com.jh.sys.bean.SysUserRole;
import com.jh.sys.bean.vo.SysUserInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysUserRoleMapper {

    /**
     * @param roleIds
     * @return List<SysUserRole>
     * 批量查询角色下所有的用户关系
     * <AUTHOR>
     */
    List<SysUserRole> selectUserByRoleIds(@Param("roleIds") String[] roleIds);

    List<SysUserInfoVO> findByRoleIdAndName(@Param("id") String id, @Param("userNames") String[] userNames);

    int deleteByUserId(@Param("userId")String userId);

    int insertSysUserRole(SysUserRole sysUserRole);

    List<SysUserRole> selectByUserIdRoleId(@Param("userId")String userId,@Param("roleId") String roleId);

    List<SysUserRole> selectByUserId(@Param("userId")String userId);

    List<SysUserRole> selectByRoleId(@Param("roleId")String id);

    int deleteByRoleId(@Param("roleId")String roleId);

    int deleteByUserIdRoleId(@Param("userId")String userId,@Param("roleId") String roleId);
}