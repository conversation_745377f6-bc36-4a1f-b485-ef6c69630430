package com.jh.sys.service;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.SysLogOper;
import com.jh.common.service.LogOperService;
import com.jh.sys.bean.vo.SysLogOperExportVo;
import com.jh.sys.bean.vo.SysLogOperVo;

import java.util.List;

/**
 * 用户操作日志表
 *
 * <AUTHOR>
 * @date 2020-07-07 17:51:45
 */
public interface SysLogOperService extends LogOperService {


    /**
     * 查询用户操作日志表详情
     *
     * @param id 日志ID
     * @return SysLogOper
     */
    SysLogOper findById(String id);

    /**
     * 分页查询用户操作日志表
     *
     * @param sysLogOperVo 日志Vo
     * @return PageInfo<SysLogOper>
     */
    PageInfo<SysLogOper> findPageByQuery(SysLogOperVo sysLogOperVo);

    /**
     * 导出日志数据
     *
     * @param sysLogOperVo 日志Vo
     * @return List<SysLogOperExportVo>
     */
    List<SysLogOperExportVo> export(SysLogOperVo sysLogOperVo);


}
