package com.jh.sys.bean.vo;

import com.jh.sys.bean.VersionHash;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 【请填写功能名称】Vo
 *<AUTHOR>
 *@date 2024-06-03
 */
@Schema(description = "VersionHashVo")
public class VersionHashVo extends VersionHash {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String versionTypeStr;
	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getVersionTypeStr() {
		return versionTypeStr;
	}

	public void setVersionTypeStr(String versionTypeStr) {
		this.versionTypeStr = versionTypeStr;
	}
}