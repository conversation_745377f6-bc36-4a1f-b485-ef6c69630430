package com.jh.sys.service;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.sys.bean.vo.SysUserVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统用户表
 *
 * <AUTHOR>
 * @date 2020-07-05 16:25:12
 */
public interface SysUserService {

    /**
     * @param sysUser 系统用户表对象
     * @return String 系统用户表ID
     * 保存或更新系统用户表
     * <AUTHOR>
     */
    String saveOrUpdate(SysUserVo sysUser);

    /**
     * @param id void 系统用户表ID
     *           删除系统用户表
     * <AUTHOR>
     */
    void deleteSysUser(List<String> id);

    /**
     * @param id
     * @return SysUser
     * 查询系统用户表详情
     * <AUTHOR>
     */
    SysUser findById(String id);

    /**
     * @param sysUserVo
     * @return PageInfo<SysUser>
     * 分页查询系统用户表
     * <AUTHOR>
     */
    PageInfo<SysUserVo> findPageByQuery(SysUserVo sysUserVo);

    /**
     * @param username
     * @return
     */
    SysUser getUserByUsername(String username);

    /**
     * @param id
     * @param roles
     * @return
     */
    Set<String> listElementByUserId(String id, List<SysRole> roles);

    /**
     * @param id
     * @return
     */
    List<SysRole> listRolesByUserId(String id);

    /**
     * 修改密码
     *
     * @param sysUser
     */
    void updatePassword(SysUserVo sysUser);

    /**
     * 通过主键修改对应用户得密码
     *
     * @param
     * @return
     */
    void resetPassword(SysUserVo sysUserVo);

    /**
     * 用户名是否存在
     *
     * @param username 用户名
     */
    void existsUserByUsername(String username);

    /**
     * 密码复杂度低时强制修改密码
     * @param sysUser
     * @param ip
     */
    void uppwd(SysUserVo sysUser, String ip);

    /**
     * 去绑定google 动态验证码
     * @param sysUser
     * @param ip
     * @return 绑定的url和密钥
     */
    Map<String, String> bindSer(SysUserVo sysUser, String ip);


    /**
     * 确认绑定google 动态验证码
     * @param sysUser
     * @param ip
     */
    void qrbindSer(SysUserVo sysUser, String ip);
}
