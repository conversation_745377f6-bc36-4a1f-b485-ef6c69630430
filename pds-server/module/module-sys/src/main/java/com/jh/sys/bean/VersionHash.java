package com.jh.sys.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 【请填写功能名称】对象 version_hash
 
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@Table(name = "version_hash")
@Schema(description = "【请填写功能名称】")
public class VersionHash extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "Version_Number")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="版本号")
	private String versionNumber;
	@Column(name = "Version_Type")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="版本类型")
	private Integer versionType;
	@Column(name = "Version_Hash")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="版本jar包MD5哈希值")
	private String versionHash;
	@Column(name = "Version_Description")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="版本说明")
	private String versionDescription;

    /**
     * SET 版本号
     * @param versionNumber
     */
    public void setVersionNumber(String versionNumber){
		this.versionNumber = versionNumber == null ? null :versionNumber;
	}
    /**
     * GET 版本号
     * @return versionNumber
     */
    public String getVersionNumber(){
        return versionNumber;
    }
    /**
     * SET 版本名称
     * @param versionType
     */
    public void setVersionType(Integer versionType){
		this.versionType = versionType;
	}
    /**
     * GET 版本名称
     * @return versionType
     */
    public Integer getVersionType(){
        return versionType;
    }
    /**
     * SET 版本jar包MD5哈希值
     * @param versionHash
     */
    public void setVersionHash(String versionHash){
		this.versionHash = versionHash == null ? null :versionHash;
	}
    /**
     * GET 版本jar包MD5哈希值
     * @return versionHash
     */
    public String getVersionHash(){
        return versionHash;
    }
    /**
     * SET 版本说明
     * @param versionDescription
     */
    public void setVersionDescription(String versionDescription){
		this.versionDescription = versionDescription == null ? null :versionDescription;
	}
    /**
     * GET 版本说明
     * @return versionDescription
     */
    public String getVersionDescription(){
        return versionDescription;
    }

    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
