package com.jh.sys.controller;


import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysUpload;
import com.jh.sys.bean.vo.SysUploadVo;
import com.jh.sys.service.SysUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 我的上传Controller
 * 
 * <AUTHOR>
 * @date 2023-10-26
 */
@RestController
@RequestMapping("/sys/upload")
@Tag(name ="我的上传")
public class SysUploadController extends BaseController {
    @Autowired
    private SysUploadService sysUploadService;

	private static final String PER_PREFIX = "btn:sys:upload:";
	
	/**
	 * 新增我的上传
	 *@param sysUpload 我的上传数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@Operation(summary="新增我的上传")
	@SystemLogAnnotation(type = "我的上传",value = "新增我的上传")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveSysUpload(@RequestBody SysUpload sysUpload) {
		String id = sysUploadService.saveOrUpdateSysUpload(sysUpload);
		return RestApiResponse.ok(id);
	}
	
	/**
	 * 修改我的上传
	 *@param sysUpload 我的上传数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@Operation(summary="修改我的上传")
	@SystemLogAnnotation(type = "我的上传",value = "修改我的上传")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateSysUpload(@RequestBody SysUpload sysUpload) {
		String id = sysUploadService.saveOrUpdateSysUpload(sysUpload);
		return RestApiResponse.ok(id);
	}
	
	/**
	 * 批量删除我的上传(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@Operation(summary= "批量删除我的上传")
	@SystemLogAnnotation(type = "我的上传",value = "批量删除我的上传")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteSysUpload(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		sysUploadService.deleteSysUpload(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 * 查询我的上传详情
	 *@param id
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@GetMapping("/findById")
	@Operation(summary="查询我的上传详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		SysUpload  sysUpload=sysUploadService.findById(id);
		return RestApiResponse.ok(sysUpload);
	}
	
	/**
	 * 分页查询我的上传
	 *@param sysUploadVo 我的上传 查询条件
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@PostMapping("/findPageByQuery")
	@Operation(summary= "分页查询我的上传")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody SysUploadVo sysUploadVo) {
		PageInfo<SysUpload> sysUpload=sysUploadService.findPageByQuery(sysUploadVo);
		return RestApiResponse.ok(sysUpload);
	}
	
}
