package com.jh.sys.util;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public  class JarHashCalculator {

    private static String extractJarPath(String input) {
        int startIndex = input.indexOf("file:");
        if (startIndex != -1) {
            startIndex += 5; // 跳过"file:"的长度
            int endIndex = input.indexOf("!/", startIndex);
            if (endIndex != -1) {
                return input.substring(startIndex, endIndex);
            }
        }else  if (input.indexOf("nested:") != -1) {
            startIndex += 9; // 跳过"file:"的长度
            int endIndex = input.indexOf("/!", startIndex);
            if (endIndex != -1) {
                return input.substring(startIndex, endIndex);
            }
        }
        return null;
    }
    /**
     *  方法描述:获取JAR MD5哈希值
     *  <AUTHOR>
     *  * @date:2024年03月03日
     * */
    public static String calculateHash() throws IOException, NoSuchAlgorithmException {
        // 获取当前 JAR 文件的路径
        String jarPath = JarHashCalculator.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        String loationPath = extractJarPath(jarPath);
        File jarFile = new File(loationPath);
        MessageDigest md = MessageDigest.getInstance("MD5");
        try(FileInputStream fis = new FileInputStream(jarFile)) {
            byte[] buffer = new byte[1024];
            int numRead;
            while ((numRead = fis.read(buffer)) != -1) {
                md.update(buffer, 0, numRead);
            }
        }
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(Integer.toString((b & 0xff) + 0x100, 16).substring(1));
        }
        return  sb.toString();
    }

    /**
     *  方法描述: 遍历获取JAR 文件旅游局
     *  <AUTHOR>
     *  * @date:2024年03月03日
     * */
    public static String findJarFiles(File dir) {
        String path=null;
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    findJarFiles(file);
                } else {
                    String fileName = file.getName().toLowerCase();
                    if (fileName.endsWith(".jar")) {
                        path=file.getAbsolutePath();
                    }
                }
            }
        }
      return  path;
    }

    /**
     *  方法描述: 去除字符串前后斜杠
     *  <AUTHOR>
     *  * @date:2024年03月03日
     * */
    public static String removeSlashes(String str) {
        while (str.startsWith("/")) {
            str = str.substring(1);
        }
        while (str.endsWith("/")) {
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

}
