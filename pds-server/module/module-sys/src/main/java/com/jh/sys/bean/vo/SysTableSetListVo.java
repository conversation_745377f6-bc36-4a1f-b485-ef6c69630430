package com.jh.sys.bean.vo;

import java.io.Serializable;
public class SysTableSetListVo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 字段中文名
     */
    private String fieldCnName;

    /**
     * 属性名称
     */
    private String fieldName;

    /**
     * 排序
     */
    private Integer orderVal;


    /**
     * 是否显示
     */
    private Boolean whetherShow = true;


    /**
     * 是否排序
     */
    private Boolean whetherSort;

    /**
     * 是否强制显示
     */
    private Boolean whetherForceShow = false;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 属性类型，0：默认显示，1 插槽显示
     */
    private Integer fieldType = 0;

    /**
     * 列表位置
     */
    private String align;

    public String getAlign() {
        return align;
    }

    public void setAlign(String align) {
        this.align = align;
    }

    public Integer getFieldType() {
        return fieldType;
    }

    public void setFieldType(Integer fieldType) {
        this.fieldType = fieldType;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Boolean getWhetherForceShow() {
        return whetherForceShow;
    }

    public void setWhetherForceShow(Boolean whetherForceShow) {
        this.whetherForceShow = whetherForceShow;
    }

    public String getFieldCnName() {
        return fieldCnName;
    }

    public void setFieldCnName(String fieldCnName) {
        this.fieldCnName = fieldCnName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public Integer getOrderVal() {
        return orderVal;
    }

    public void setOrderVal(Integer orderVal) {
        this.orderVal = orderVal;
    }

    public Boolean getWhetherShow() {
        return whetherShow;
    }

    public void setWhetherShow(Boolean whetherShow) {
        this.whetherShow = whetherShow;
    }

    public Boolean getWhetherSort() {
        return whetherSort;
    }

    public void setWhetherSort(Boolean whetherSort) {
        this.whetherSort = whetherSort;
    }
}
