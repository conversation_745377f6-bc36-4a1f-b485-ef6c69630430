<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysDictItemMapper">
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="YN" property="yn" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 系统数据字典分类 -->
    <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysDictItem" extends="CommonBaseResultMap">
        <!--
          WARNING - @mbg.generated
        -->
        <!-- 状态（启用,停用） -->
        <result column="STATUS_TXT" property="statusTxt" jdbcType="VARCHAR"/>
        <!-- 状态（1启用,0停用） -->
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <!-- 排序值 -->
        <result column="ORDER_VAL" property="orderVal" jdbcType="INTEGER"/>
        <!-- 条目说明 -->
        <result column="ITEM_DESC" property="itemDesc" jdbcType="VARCHAR"/>
        <!-- 业务编码 -->
        <result column="ITEM_VALUE" property="itemValue" jdbcType="VARCHAR"/>
        <!-- 条目编码 -->
        <result column="ITEM_CODE" property="itemCode" jdbcType="VARCHAR"/>
        <!-- 父节点CODE -->
        <result column="PARENT_CODE" property="parentCode" jdbcType="VARCHAR"/>
        <!--  -->
        <result column="IS_LEAF" property="isLeaf" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findDictItemByLikeDictType"
            resultMap="BaseResultMap">
        SELECT
        *
        FROM
        sys_dict_item t
        WHERE
        1=1
        <if test="from==null or from==''">
            and t.STATUS=1
        </if>
        <if test="itemValue!=null and itemValue!=''">
            <bind name="searchItemValue" value="'%' + itemValue + '%'"/>
            and t.ITEM_VALUE like #{searchItemValue}
        </if>
        <if test="codeList!=null and codeList.size()>0">
            and
            <foreach collection="codeList" item="code" open="("
                     close=")" separator="or">
                <if test="code !=null or code!=''">
                    <bind name="searchCode" value="code + '%'"/>
                    t.ITEM_CODE like #{searchCode} and t.item_code!='NAV_CONTRACT_FUND'
                </if>
            </foreach>
        </if>
        order by
        <if test="from==null or from==''">
            t.item_code asc,
        </if>
        t.ORDER_VAL asc
    </select>

    <!--   根据字典CODE得到下级结点列表一级 -->
    <select id="findDictItemByDictType" resultMap="BaseResultMap">
	SELECT
		t.*
	FROM
		sys_dict_item t
	WHERE
		t.STATUS = 1 AND T.YN = 1
	AND EXISTS (
		SELECT
			1
		FROM
			sys_dict_item d
		WHERE
			d.Item_Code = #{typeCode}
		AND d.Item_Code = t.Parent_code
	)
	ORDER BY  t.Order_Val ASC
  </select>
    <!--   得到字典 treeDrop -->
    <select id="getTreeDrop" resultType="com.jh.sys.bean.vo.TreeDropVo">
WITH RECURSIVE dictLit(item_code, item_value, parent_code,order_Val,code_path) AS (
		SELECT
			item_code, item_value, parent_code,order_Val,item_code as code_path
		FROM
			sys_dict_item 
		WHERE
			parent_code = '0'
	    UNION ALL
		SELECT
			b.item_code,
			b.item_value,
			b.parent_code,
			b.order_Val,
	concat( a.code_path, "^^", b.item_code) AS code_path
		FROM
			dictLit a,
			sys_dict_item b 
		WHERE
			a.item_code = b.parent_code 
	)
	SELECT item_code as code, item_value as name, parent_code,code_path FROM dictLit 
	where code_path like 
	(select CONCAT(code_path,'^^%') from dictLit where item_code=#{itemCode})
	order by order_Val asc

  </select>

	<!-- 根据ItemCode获取ItemName -->
    <select id="findByItemCode" resultType="com.jh.sys.bean.SysDictItem">
		SELECT A.* FROM SYS_DICT_ITEM AS A WHERE ITEM_CODE = #{itemCode}
    </select>
</mapper>