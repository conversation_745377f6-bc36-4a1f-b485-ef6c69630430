package com.jh.sys.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.bean.SysRole;
import com.jh.common.controller.BaseController;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.sys.bean.vo.RoleAndRoleTypesVO;
import com.jh.sys.bean.vo.SysRoleExportVo;
import com.jh.sys.bean.vo.SysRoleVo;
import com.jh.sys.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色 controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/role")
@Tag(name = "角色接口")
public class RoleController extends BaseController {

    private static final String prefix = "btn:sys:role:";
    @Autowired
    private RoleService roleService;

    @RepeatSubAnnotation
    @PostMapping(value = "/addRole")
      @Operation(summary="保存角色")
    @SystemLogAnnotation(type = "保存角色", value = "保存角色")
    @PreAuthorize("hasAuthority('" + prefix + "add')")
    public RestApiResponse<?> addRole(@RequestBody SysRoleVo sysRole) throws Exception {
        SysRole role = roleService.saveOrUpdateRole(sysRole);
        return RestApiResponse.ok(role);
    }

    @RepeatSubAnnotation
    @PostMapping(value = "/updateRole")
      @Operation(summary="更新角色")
    @SystemLogAnnotation(type = "更新角色", value = "更新角色")
    @PreAuthorize("hasAuthority('" + prefix + "edit')")
    public @ResponseBody
    RestApiResponse<?> updateRole(@RequestBody SysRoleVo sysRole) throws Exception {
        return RestApiResponse.ok(this.roleService.saveOrUpdateRole(sysRole));
    }

    /**
     * 批量删除角色
     *
     * @param roleIds 角色Ids
     * @return 结果
     */
    @RepeatSubAnnotation
    @GetMapping(value = "/deleteRole/{roleIds}")
      @Operation(summary="批量删除角色")
    @ResponseBody
    @PreAuthorize("hasAuthority('" + prefix + "delete')")
    public RestApiResponse<?> deleteRole(@PathVariable("roleIds") String roleIds) {
        roleService.deleteRole(roleIds);
        return RestApiResponse.ok();
    }

    /**
     * 查询角色列表
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    @PostMapping(value = "/listPage")
      @Operation(summary="角色名称模糊查询角色(分页)")
    @PreAuthorize("hasAuthority('" + prefix + "query')")
    public RestApiResponse<?> listRolePage(@RequestBody SysRoleVo sysRoleVo) {
        sysRoleVo.setAdmin(isAdmin());
        PageInfo<RoleAndRoleTypesVO> roleTypeVo = this.roleService.listPageRoleAndRoleTypeVo(sysRoleVo);
        return RestApiResponse.ok(roleTypeVo);
    }

    /**
     * 获取登录用户所属角色
     *
     * @return 角色
     */
    @PostMapping(value = "/listRoleByLoginUser")
      @Operation(summary="查询当前用户添加的角色列表")
    public RestApiResponse<?> listRoleByLoginUser() {
        SysRoleVo sysRoleVo = new SysRoleVo();
        sysRoleVo.setAdmin(isAdmin());
        List<SysRole> roleList = this.roleService.listRoleByLoginUser(sysRoleVo);
        return RestApiResponse.ok(roleList);
    }

    /**
     * 根据角色ID查询角色
     *
     * @param id 角色ID
     * @return 结果
     */
    @GetMapping(value = "/findById/{id}")
      @Operation(summary="根据角色ID查询角色")
    @PreAuthorize("hasAuthority('" + prefix + "byid')")
    public RestApiResponse<?> findById(@PathVariable("id") String id) {
        SysRoleVo sysRole = roleService.findById(id);
        return RestApiResponse.ok(sysRole);
    }

    /**
     * 启用/禁用角色
     *
     * @param roleIds 角色ID
     * @param status  状态
     * @return 结果
     */
    @RepeatSubAnnotation
    @GetMapping(value = "/enable/{roleIds}/{status}")
      @Operation(summary="改变角色状态-启用")
    @PreAuthorize("hasAuthority('" + prefix + "enable')")
    public @ResponseBody
    RestApiResponse<?> enable(@PathVariable("roleIds") String roleIds, @PathVariable("status") Integer status) {
        roleService.changeStatus(roleIds, status);
        return RestApiResponse.ok();

    }
    /**
     * 启用/禁用 角色动态验证
     *
     * @param roleIds 角色ID
     * @param status  状态
     * @return 结果
     */
    @RepeatSubAnnotation
    @GetMapping(value = "/enableDynamicVer/{roleIds}/{status}")
      @Operation(summary="改变角色动态验证状态-启用")
    @PreAuthorize("hasAuthority('" + prefix + "enable')")
    public @ResponseBody
    RestApiResponse<?> enableDynamicVer(@PathVariable("roleIds") String roleIds, @PathVariable("status") Integer status) {
        roleService.enableDynamicVer(roleIds, status);
        return RestApiResponse.ok();

    }


    /**
     * 导出
     *
     * @param sysRoleVo 角色Vo
     */
    @PostMapping(value = "/export")
      @Operation(summary="导出角色")
    @PreAuthorize("hasAuthority('" + prefix + "export')")
    public void export(SysRoleVo sysRoleVo) {
        sysRoleVo.setAdmin(isAdmin());
        List<SysRoleExportVo> roleList = roleService.export(sysRoleVo);
        EasyExcelUtils.exportExcel(roleList, "导出角色结果", SysRoleExportVo.class, "导出角色结果.xlsx", response);
    }

}
