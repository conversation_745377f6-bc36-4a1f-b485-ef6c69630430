package com.jh.sys.bean.vo;

import com.jh.common.bean.SysRole;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 角色 详情 ----包含角色所属角色类型
 */
public class RoleAndRoleTypesVO extends SysRole {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 是否是当前用户添加的角色，true是；false否
     */
    private boolean isAdmin;

    /**
     * 用户类型
     */
    private String userType;

    private String roleTypes;

    private String roleTypeNames;

    public boolean isAdmin() {
        return isAdmin;
    }

    public void setAdmin(boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getRoleTypes() {
        return roleTypes;
    }

    public void setRoleTypes(String roleTypes) {
        this.roleTypes = roleTypes;
    }

    public String getRoleTypeNames() {
        return roleTypeNames;
    }

    public void setRoleTypeNames(String roleTypeNames) {
        this.roleTypeNames = roleTypeNames;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}