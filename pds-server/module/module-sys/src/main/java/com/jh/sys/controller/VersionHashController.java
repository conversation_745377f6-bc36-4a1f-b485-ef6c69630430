package com.jh.sys.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.sys.util.JarHashCalculator;
import com.jh.sys.bean.VersionHash;
import com.jh.sys.bean.vo.VersionHashVo;
import com.jh.sys.service.VersionHashService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/version/hash")
@Tag(name="【请填写功能名称】")
public class VersionHashController extends BaseController {
    @Autowired
    private VersionHashService versionHashService;

	private static final String PER_PREFIX = "btn:sys:hash:";
	
	/**
	 * 新增【版本信息】
	 *@param versionHash 【版本信息】数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */

	@PostMapping("/save")
	  @Operation(summary="新增【请填写功能名称】")
	@SystemLogAnnotation(type = "【请填写功能名称】",value = "新增【请填写功能名称】")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
	public RestApiResponse<?> saveVersionHash(@RequestBody VersionHash versionHash) throws IOException, NoSuchAlgorithmException {

		versionHash.setVersionHash(JarHashCalculator.calculateHash());
		String id = versionHashService.saveOrUpdateVersionHash(versionHash);
		return RestApiResponse.ok(id);
	}

	/**
	 * 查询【获取版本详情】详情
	 *@param
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@GetMapping("/findByVersionHash")
	  @Operation(summary="查询【请填写功能名称】详情")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "get')")
	public RestApiResponse<?> findByVersionHash() throws IOException, NoSuchAlgorithmException {
		try {
			JarHashCalculator.calculateHash();
		}catch (Exception e){
			return RestApiResponse.ok(null);
		}
		VersionHash  versionHash=versionHashService.findByVersionHash();
		if (versionHash==null){
			VersionHash ver=new VersionHash();
			ver.setVersionNumber("V1.0.0");
			ver.setVersionType(4);
			return RestApiResponse.ok(ver);
		}
		//获取最新Hash值
		String hash=versionHash.getVersionHash();
        //获取jar包版本值
		try {
			String jarHash= JarHashCalculator.calculateHash();
			//比较Hash值是否相等
			if(!hash.equals(jarHash))
			{
				versionHash.setVersionType(4);
				return RestApiResponse.ok(versionHash);
			}
		}catch (Exception e){}
		return RestApiResponse.ok(versionHash);
	}
	
	/**
	 * 分页查询【请填写功能名称】
	 *@param versionHashVo 【请填写功能名称】 查询条件
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@PostMapping("/findPageByQuery")
	@Operation(summary="分页查询【请填写功能名称】")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody VersionHashVo versionHashVo) {
		PageInfo<VersionHash> versionHash=versionHashService.findPageByQuery(versionHashVo);
		return RestApiResponse.ok(versionHash);
	}


	@PostMapping("/excel")
	@Operation(summary="导出版本")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
	@ExportRespAnnotation(modType = "系统管理", sonMod = "版本管理", key = "sys_version_table", isNotice = true,  fileName = "系统版本")
	public RestApiResponse<?> excel(@RequestBody VersionHashVo versionHashVo) {
		List<VersionHash> versionHash=versionHashService.findByQuery(versionHashVo);
		List<VersionHashVo> versionHashVoList=new ArrayList<>(versionHash.size());
		for(VersionHash v:versionHash){
			VersionHashVo vo=new VersionHashVo();
			BeanUtils.copyProperties(v,vo);
			switch (v.getVersionType()){
				case 1:
					vo.setVersionTypeStr("大版本升级");
					break;
				case 2:
					vo.setVersionTypeStr("功能增加");
					break;
				case 3:
					vo.setVersionTypeStr("bug修复");
					break;
			}
			versionHashVoList.add(vo);
		}
		return RestApiResponse.ok(versionHashVoList);
	}
}
