package com.jh.sys.bean.vo;

import com.jh.sys.bean.SysDictItem;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 系统数据字典分类Vo
 *
 * <AUTHOR>
 * @date 2020-07-05 15:14:51
 */
@Schema(description = "系统数据字典分类Vo")
public class SysDictItemVo extends SysDictItem {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    private String codes;
    private List<String> codeList;
    private String from;

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getCodes() {
        return codes;
    }

    public void setCodes(String codes) {
        this.codes = codes;
    }

    public List<String> getCodeList() {
        return codeList;
    }

    public void setCodeList(List<String> codeList) {
        this.codeList = codeList;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

}
