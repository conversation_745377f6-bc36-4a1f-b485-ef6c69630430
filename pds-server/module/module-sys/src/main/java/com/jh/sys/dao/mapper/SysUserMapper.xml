<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysUserMapper">
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR" />
        <result column="YN" property="yn" jdbcType="INTEGER" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
        <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
        <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
    </resultMap>
    <!-- 系统用户表 -->
    <resultMap id="BaseResultMap" type="com.jh.common.bean.SysUser" extends="CommonBaseResultMap">
        <!-- WARNING - @mbg.generated -->
        <!-- 类型 -->
        <result column="TYPE" property="type" jdbcType="VARCHAR" />
        <result column="TYPENAME" property="type" jdbcType="VARCHAR" />
        <!-- 状态（1有效,0无效） -->
        <result column="ENABLED" property="enabled" jdbcType="INTEGER" />
        <!-- 性别 -->
        <result column="SEX" property="sex" jdbcType="VARCHAR" />
        <!-- 电话 -->
        <result column="PHONE" property="phone" jdbcType="VARCHAR" />
        <!-- 手机号区号 -->
        <result column="PHONE_COUNTRY" property="phoneCountry" jdbcType="VARCHAR" />
        <!-- 头像url -->
        <result column="HEAD_IMG_URL" property="headImgUrl" jdbcType="VARCHAR" />
        <!-- 昵称 -->
        <result column="NICKNAME" property="nickname" jdbcType="VARCHAR" />
        <!-- 用户名 -->
        <result column="USERNAME" property="username" jdbcType="VARCHAR" />
        <!-- 密码 -->
        <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
        <!-- 盐 -->
        <result column="SALT" property="salt" jdbcType="VARCHAR" />
        <!-- 最后登录时间 -->
        <result column="LAST_LOGIN" property="lastLogin" jdbcType="TIMESTAMP" />
        <!-- 备注信息 -->
        <result column="REMARK" property="remark" jdbcType="VARCHAR" />
        <!-- 行政区划 -->
        <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR" />
        <!-- 行政区划名称 -->
        <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="sys_user_field">
        su.ID, su.TYPE, su.TYPENAME, su.ENABLED, su.SEX, su.PHONE, su.PHONE_COUNTRY, su.HEAD_IMG_URL, su.NICKNAME, su.USERNAME, su.CREATE_TIME, su.UPDATE_TIME, su.YN,
    </sql>
    <!-- 业务逻辑SQL -->
    <select id="findPageByQuery" resultType="com.jh.sys.bean.vo.SysUserVo">
        SELECT
        su.ID, su.TYPE, su.TYPENAME, su.ENABLED, su.SEX, su.PHONE, su.PHONE_COUNTRY, su.HEAD_IMG_URL, su.NICKNAME,
        su.USERNAME, su.CREATE_TIME, su.UPDATE_TIME, su.YN,
        (SELECT GROUP_CONCAT(DISTINCT(r.role_name)) FROM sys_role r, sys_user_role ur WHERE r.id = ur.role_id AND
        ur.user_id = su.id) AS roleName,
        (SELECT GROUP_CONCAT(DISTINCT(r.id)) FROM sys_role r, sys_user_role ur WHERE r.id = ur.role_id AND ur.user_id =
        su.id ) AS displayRoleIds,
        (SELECT GROUP_CONCAT(DISTINCT(r.role_code)) FROM sys_role r, sys_user_role ur WHERE r.id = ur.role_id AND
        ur.user_id = su.id ) AS displayRoleCodes
        FROM
        sys_user su
        WHERE
        su.YN = 1
        <if test="vo.id != null and vo.id != ''">
            AND su.ID = #{vo.id}
        </if>
        <if test="vo.username != null and vo.username != ''">
            <bind name="nick" value="'%' + vo.username + '%'"/>
            AND su.USERNAME LIKE #{nick}
        </if>

        <if test="vo.roleCode != null and vo.roleCode != ''">
            AND EXISTS(SELECT 1 FROM sys_user_role sur WHERE sur.role_id = # { vo.roleCode } AND sur.USER_ID = su.ID )
        </if>
        <if test="vo.roleName != null and vo.roleName != ''">
            AND EXISTS(SELECT 1 FROM sys_role sr1, sys_user_role sur1 WHERE sr1.id = sur1.role_id AND sur1.user_id =
            su.id AND sr1.role_code = #{vo.roleName} )
        </if>
        ORDER BY su.CREATE_TIME DESC
        <if test="vo.sortField != null and vo.sortField !='' and vo.sortType != null and vo.sortType !=''">
            ,${vo.sortField} ${vo.sortType}
        </if>
    </select>
    <!-- 查询一个市级用户 -->
    <select id="findOneCityUser" resultType="com.jh.common.bean.SysUser">
        SELECT A.*
        FROM (sys_user A, sys_user_role B, sys_role C)
        WHERE A.ID = B.USER_ID
          AND C.ID = B.ROLE_ID
          AND C.ROLE_CODE = 'DEPT_CITY'
          AND A.YN = 1
          AND B.YN = 1
          AND C.YN = 1 limit 1
    </select>

    <!-- 查询一个省级用户 -->
    <select id="findOneProvinceUser" resultType="com.jh.common.bean.SysUser">
        SELECT A.*
        FROM (sys_user A, sys_user_role B, sys_role C)
        WHERE A.ID = B.USER_ID
          AND C.ID = B.ROLE_ID
          AND C.ROLE_CODE = 'DEPT_PROVINCE'
          AND A.YN = 1
          AND B.YN = 1
          AND C.YN = 1 limit 1
    </select>

    <update id="updateYnByIds" parameterType="java.util.List">
        <if test="ids != null">
            UPDATE sys_user
            SET YN = 0
            WHERE ID IN
            <foreach collection="ids" item="item" index="index"
                     separator="," open="(" close=")">
                #{item}
            </foreach>
            ;

            delete from sys_user_role where user_id in
            <foreach collection="ids" item="item" index="index"
                     separator="," open="(" close=")">
                #{item}
            </foreach>
            ;
        </if>
    </update>

    <select id="getUserByMorgId" resultType="com.jh.sys.bean.vo.SysUserVo">
        SELECT A.*
        FROM (sys_user A, sys_user_role B, sys_role C)
        WHERE A.ID = B.USER_ID
          AND C.ID = B.ROLE_ID
          AND MANAGE_ORG_ID = #{morgId}
          AND (C.ROLE_CODE = 'DEPT_CITY' OR C.ROLE_CODE = 'DEPT_COUNTY') limit 1
    </select>

    <!-- 根据areacode查询用户【监管单位用户】 -->
    <select id="findMorgByAreaCode" resultType="com.jh.common.bean.SysUser">
        SELECT *
        FROM SYS_USER
        WHERE AREA_CODE = #{areaCode}
          AND MANAGE_ORG_ID IS NOT NULL
          AND MANAGE_ORG_ID != ''
    </select>

    <!-- 根据areacode查询用户【科技管理部門用户】 -->
    <select id="findScienceByAreaCode" resultType="com.jh.common.bean.SysUser">
        SELECT *
        FROM SYS_USER
        WHERE AREA_CODE = #{areaCode}
          AND SCIENCE_ORG_ID IS NOT NULL
          AND SCIENCE_ORG_ID != ''
    </select>

    <!-- 根据Username查询用户 -->
    <select id="findByUsername" resultType="com.jh.common.bean.SysUser">
        SELECT *
        FROM SYS_USER
        WHERE USERNAME = #{userName}
          AND YN = 1
    </select>

    <!-- 根据scienceOrgId获取列表 -->
    <select id="listByScienceOrgId" resultType="com.jh.common.bean.SysUser">
        SELECT *
        FROM SYS_USER
        WHERE SCIENCE_ORG_ID = #{scienceOrgId}
          AND YN = 1
    </select>

    <!-- 根据角色CODE查询所有角色 -->
    <select id="findPageByRoleCode" resultType="com.jh.common.bean.SysUser">
        SELECT sUser.*
        FROM SYS_USER sUser
            RIGHT JOIN(select sUserRole.*
                            from SYS_USER_ROLE sUserRole RIGHT JOIN(select * from SYS_ROLE where ROLE_CODE = # { roleCode }) a ON sUserRole.ROLE_ID = a.ID) b ON sUser.ID = b.USER_ID
    </select>

    <select id="findExpertPageByQuery" resultType="com.jh.sys.bean.vo.SysUserVo">
        SELECT su.*
             , bp.NAME           as name
             , bp.ID_CARD_NUMBER as idCardNumbe
             , bp.DEGREE         as degree
             , bp.POST           as post
             , bp.TITLE_NAME     as titleName
             , bp.UNIT_NAME      as unitName
             , bp.BIRTHDAY       as birthday
        FROM sys_user su
                 left join sys_user_role sur on su.id = sur.USER_ID
                 left join sys_role sr on sur.ROLE_ID = sr.id
                 left join basic_person bp on bp.USER_ID = su.id
        where sr.role_code = 'EXPERT'
    </select>

    <sql id="sql_login_where">
        WHERE yn=1 and (USERNAME=#{username} or PHONE=#{username})
    </sql>

    <!-- 根据用户名获取用户信息 -->
    <select id="getUserByUsername" resultType="com.jh.common.bean.SysUser">
        SELECT `ID`, `TYPE`, `TYPENAME`, `ENABLED`, `SEX`, `PHONE`, `PHONE_COUNTRY`, `HEAD_IMG_URL`, `NICKNAME`,
        `USERNAME`, `PASSWORD`, `SALT`, `LAST_LOGIN`, `REMARK`, `AREA_CODE`, `AREA_NAME`
        FROM sys_user
        <include refid="sql_login_where"></include>
    </select>

    <!-- 根据用户名获取用户数量 -->
    <select id="countUserByUsername" resultType="java.lang.Integer">
        select count(1) from sys_user
        <include refid="sql_login_where"></include>
    </select>

    <!-- 判断手机号是否存在 -->
    <select id="countUserByPhone" resultType="java.lang.Integer">
        select count(1)
        from sys_user
        where PHONE = #{phone}
    </select>

</mapper>
