package com.jh.sys.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 角色权限
 *
 * <AUTHOR>
 * @date 2019-12-27 13:33:33
 */
@Table(name = "SYS_ROLE_PERMISSION")
@Schema(description = "角色权限")
public class SysRolePermission extends BaseEntity {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Column(name = "ORDER_VAL")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序值")
    private Integer orderVal;

    @Column(name = "ROLE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "角色编码")
    private String roleCode;

    @Column(name = "RESOURCE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "资源ID")
    private String resourceId;

    @Column(name = "RESOURCE_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="资源类型菜单")
    private String resourceType;

    /**
     * GET 排序值
     *
     * @return orderVal
     */
    public Integer getOrderVal() {
        return orderVal;
    }

    /**
     * SET 排序值
     *
     * @param orderVal
     */
    public void setOrderVal(Integer orderVal) {
        this.orderVal = orderVal;
    }

    /**
     * GET 角色编码
     *
     * @return roleCode
     */
    public String getRoleCode() {
        return roleCode;
    }

    /**
     * SET 角色编码
     *
     * @param roleCode
     */
    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode == null ? null : roleCode.trim();
    }

    /**
     * GET 资源ID
     *
     * @return resourceId
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * SET 资源ID
     *
     * @param resourceId
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * GET 资源类型 button=按钮  menu=菜单
     *
     * @return resourceType
     */
    public String getResourceType() {
        return resourceType;
    }

    /**
     * SET 资源类型 button=按钮  menu=菜单
     *
     * @param resourceType
     */
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}