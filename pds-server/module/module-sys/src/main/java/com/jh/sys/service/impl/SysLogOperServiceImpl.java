package com.jh.sys.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.SysLogOper;
import com.jh.common.constant.Constant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.vo.SysLogOperExportVo;
import com.jh.sys.bean.vo.SysLogOperVo;
import com.jh.sys.dao.SysLogOperMapper;
import com.jh.common.util.AssertUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.service.SysLogOperService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 * 用户操作日志表
 *
 * <AUTHOR>
 * @date 2020-07-07 17:51:45
 */
@Service
@Transactional(readOnly = true)
public class SysLogOperServiceImpl extends BaseServiceImpl<SysLogOperMapper, SysLogOper> implements SysLogOperService {

    public static final Logger logger = LoggerFactory.getLogger(SysLogOperServiceImpl.class);

    @Autowired
    private SysLogOperMapper sysLogOperMapper;

    /**
     * 保存或更新用户操作日志表
     *
     * @param sysLogOper 用户操作日志表对象
     * @return String 用户操作日志表ID
     */
    @Override
    @Transactional(readOnly = false)
    public String saveOrUpdateSysLogOper(SysLogOper sysLogOper) {
        AssertUtil.notNull(sysLogOper);
        if (ObjectUtils.isEmpty(sysLogOper.getId())) {
            // 新增
            sysLogOper.setId(UUIDUtils.getUUID());
            sysLogOperMapper.insertSelective(sysLogOper);
        } else {
            // 避免页面传入修改
            sysLogOper.setYn(null);
            sysLogOperMapper.updateByPrimaryKeySelective(sysLogOper);
        }
        return sysLogOper.getId();
    }

    /**
     * 查询用户操作日志表详情
     *
     * @param id 日志ID
     * @return SysLogOper
     */
    @Override
    public SysLogOper findById(String id) {
        return sysLogOperMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页查询用户操作日志表
     *
     * @param sysLogOperVo 日志Vo
     * @return 分页数据
     */
    @Override
    public PageInfo<SysLogOper> findPageByQuery(SysLogOperVo sysLogOperVo) {
        PageHelper.startPage(sysLogOperVo.getPageNum(), sysLogOperVo.getPageSize());
        Example example = new Example(SysLogOper.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.YES);
        // 查询条件
        String userName = sysLogOperVo.getCreateUserNickname();
        String operTwoModule = sysLogOperVo.getOperTwoModule();
        String operDetails = sysLogOperVo.getOperDetails();
        List<String> operTime = sysLogOperVo.getOperTime();
        String sortField = sysLogOperVo.getSortField();
        if (!StringUtils.isEmpty(userName)) {
            criteria.andLike("createUserNickname", "%" + userName + "%");
        }

        if (!StringUtils.isEmpty(operTwoModule)) {
            Criteria o = example.createCriteria();
            o.orLike("operOneModule", "%" + operTwoModule + "%").orLike("operTwoModule", "%" + operTwoModule + "%");
            example.and(o);
        }

        if (!StringUtils.isEmpty(operDetails)) {
            criteria.andLike("operDetails", "%" + operDetails + "%");
        }

        if (operTime != null && operTime.size() == 2) {
            criteria.andBetween("createTime", operTime.get(0), operTime.get(1));
        }

        if (!StringUtils.isEmpty(sortField)) {
            if ("desc".equals(sysLogOperVo.getSortType())) {
                example.orderBy(sortField).desc();
            } else {
                example.orderBy(sortField).asc();
            }
        } else {
            example.orderBy("createTime").desc();
        }
        List<SysLogOper> sysLogOperList = sysLogOperMapper.selectByExample(example);
        return new PageInfo<SysLogOper>(sysLogOperList);
    }

    /**
     * 导出日志数据
     *
     * @param sysLogOperVo 日志Vo
     */
    @Override
    public List<SysLogOperExportVo> export(SysLogOperVo sysLogOperVo) {
        return sysLogOperMapper.selectExportData(sysLogOperVo);
    }

}
