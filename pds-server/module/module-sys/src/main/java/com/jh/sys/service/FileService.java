package com.jh.sys.service;

import com.jh.common.bean.SysFileInfo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

public interface FileService {

    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    SysFileInfo upload(MultipartFile file) throws Exception;

    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    SysFileInfo upload(MultipartFile file, SysFileInfo fileInfo) throws Exception;

    /**
     * 上传配置文件
     *
     * @param file
     * @param fileInfo
     * @param markPath
     * @return
     * @throws Exception
     */
    SysFileInfo upload(MultipartFile file, SysFileInfo fileInfo, String markPath) throws Exception;

    /**
     * 上传本地文件
     *
     * @param file
     * @param fileInfo
     * @return
     * @throws Exception
     */
    SysFileInfo upload(File file, SysFileInfo fileInfo) throws Exception;

    /**
     * 删除文件
     *
     * @param fileInfo
     */
    void delete(SysFileInfo fileInfo);

    /**
     * 文件下载
     *
     * @param key
     * @param response
     */
    void down(String key, HttpServletResponse response);

    boolean copyFile(String source, String taraget);
}
