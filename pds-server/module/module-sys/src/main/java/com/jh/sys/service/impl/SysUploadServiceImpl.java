package com.jh.sys.service.impl;

import com.github.pagehelper.PageHelper;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.LoginUser;
import com.jh.common.constant.CommonConstant;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.bean.SysUpload;
import com.jh.sys.bean.vo.SysUploadVo;
import com.jh.sys.dao.SysUploadMapper;
import com.jh.sys.service.SysUploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 *  我的上传Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Service
@Transactional(readOnly = true)
public class SysUploadServiceImpl extends BaseServiceImpl<SysUploadMapper, SysUpload> implements SysUploadService {

	private static final Logger logger = LoggerFactory.getLogger(SysUploadServiceImpl.class);
    @Autowired
    private SysUploadMapper sysUploadMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新我的上传
	 *@param sysUpload 我的上传对象
	 *@return String 我的上传ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateSysUpload(SysUpload sysUpload) {
		String did= "11";
		if(sysUpload==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(sysUpload.getId())){

			//新增
			sysUpload.setId(UUIDUtils.getUUID());
			sysUploadMapper.insertSelective(sysUpload);
		}else{
			//避免页面传入修改
			sysUpload.setYn(null);
			sysUploadMapper.updateByPrimaryKeySelective(sysUpload);
		}
		return sysUpload.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除我的上传
	 *@param ids void 我的上传ID
	 *<AUTHOR>
	 */
	public void deleteSysUpload(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除
			SysUpload sysUpload=sysUploadMapper.selectByPrimaryKey(id);
			if(sysUpload==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			SysUpload temsysUpload=new SysUpload();
			temsysUpload.setYn(CommonConstant.FLAG_NO);
			temsysUpload.setId(sysUpload.getId());
			sysUploadMapper.updateByPrimaryKeySelective(temsysUpload);
		}
	}

	/**
	 * 查询我的上传详情
	 *@param id
	 *@return SysUpload
	 *<AUTHOR>
	 */
    @Override
	public SysUpload findById(String id) {
		return sysUploadMapper.selectByPrimaryKey(id);
	}


	/**
	 * 分页查询我的上传
	 *@param sysUploadVo
	 *@return PageInfo<SysUpload>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<SysUpload> findPageByQuery(SysUploadVo sysUploadVo) {
		PageHelper.startPage(sysUploadVo.getPageNum(),sysUploadVo.getPageSize());
		Example example=new Example(SysUpload.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if (!StringUtils.isEmpty(sysUploadVo.getUploadTimeStart())) {
			criteria.andBetween("uploadTime", sysUploadVo.getUploadTimeStart()+" 00:00:00", sysUploadVo.getUploadTimeEnd()+" 23:59:59");
		}
		if (!StringUtils.isEmpty(sysUploadVo.getModType())) {
			criteria.andLike("modType","%"+sysUploadVo.getModType()+"%");
		}
		if (!StringUtils.isEmpty(sysUploadVo.getSonMod())) {
			criteria.andLike("sonMod","%"+sysUploadVo.getSonMod()+"%");
		}
		LoginUser appLoginUser = (LoginUser) AppUserUtil.getLoginAppUser();
		if (appLoginUser != null) {
			criteria.andEqualTo("userId",appLoginUser.getId());
		}
		example.orderBy("uploadTime").desc();
		List<SysUpload> sysUploadList=sysUploadMapper.selectByExample(example);
		return new PageInfo<SysUpload>(sysUploadList);
	}
}
