package com.jh.sys.service;

import com.jh.sys.bean.SysTableSet;
import com.jh.sys.bean.vo.DataFiled;
import com.jh.sys.bean.vo.SysTableSetVo;

import java.util.List;
/**
 * 用户格设置Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface SysTableSetService{

	/**
	 * 获取用户自定义列表
	 *
	 * @param key
	 * @param userId
	 * @return
	 */
	SysTableSet findByKeyAndUserId(String key, String userId);

	/**
	 * 获取用户自定义导出属性
	 * @param key
	 * @param currentUserId
	 * @return
	 */
	SysTableSet findByCurrentUserForExport(String key, String currentUserId);

	/**
	 * 获取用户自定义列表
	 *
	 * @param key
	 * @param userId
	 * @return
	 */
	SysTableSet findByKeyAndUserIdForInner(String key, String userId);


	/**
	 * 根据key跟userId获取数据库中保存的excel导出字段
	 *
	 * @param key
	 * @param userId
	 */
	List<DataFiled> getExportColumns(String key, String userId);

	/**
	 * 保存或更新列表显示字段
	 * @param sysTableSetVo
	 * @return
	 */
	String saveListTableset(SysTableSetVo sysTableSetVo);


}
