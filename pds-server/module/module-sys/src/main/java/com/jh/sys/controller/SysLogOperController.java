package com.jh.sys.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.bean.SysLogOper;
import com.jh.common.controller.BaseController;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.sys.bean.vo.SysLogOperExportVo;
import com.jh.sys.bean.vo.SysLogOperVo;
import com.jh.sys.service.SysLogOperService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户操作日志表
 *
 * <AUTHOR>
 * @date 2020-07-07 17:51:45
 */
@RestController
@RequestMapping("/sys/sysLogOper")
@Tag(name = "用户操作日志表")
public class SysLogOperController extends BaseController {

    private static final String PER_PREFIX = "btn:sys:operlog:";
    @Autowired
    private SysLogOperService sysLogOperService;

    /**
     * 分页查询用户操作日志表
     *
     * @param sysLogOperVo 用户操作日志表 查询条件
     * @return RestApiResponse<?>
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询用户操作日志表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysLogOperVo sysLogOperVo) {
        PageInfo<SysLogOper> sysLogOper = sysLogOperService.findPageByQuery(sysLogOperVo);
        return RestApiResponse.ok(sysLogOper);
    }

    /**
     * 导出操作日志
     *
     * @param sysLogOperVo 日志Vo
     * <AUTHOR>
     */
    @PostMapping("/export")
      @Operation(summary="导出操作日志")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "export')")
    public void export(SysLogOperVo sysLogOperVo) {
        List<SysLogOperExportVo> list = sysLogOperService.export(sysLogOperVo);
        EasyExcelUtils.exportExcel(list, "导出操作日志结果", SysLogOperExportVo.class, "导出操作日志结果", response);
    }

}
