package com.jh.sys.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.bean.SysLogRecord;
import com.jh.common.controller.BaseController;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.sys.bean.vo.SysLogRecordExportVo;
import com.jh.sys.bean.vo.SysLogRecordVo;
import com.jh.sys.service.SysLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @date 2019-11-04 14:33:24
 */
@RestController
@RequestMapping("/sys/accesslog")
@Tag(name = "系统日志")
public class SystemLogController extends BaseController {

    private static final String PER_PREFIX = "btn:sys:log:";
    @Autowired
    private SysLogService sysLogService;

    /**
     * 分页查询用户操作日志表
     *
     * @param sysLogRecordVo 用户操作日志表 查询条件
     * @return RestApiResponse<?>
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询用户操作日志表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysLogRecordVo sysLogRecordVo) {
        PageInfo<SysLogRecord> sysLogOper = sysLogService.findPageByQuery(sysLogRecordVo);
        return RestApiResponse.ok(sysLogOper);
    }

    /**
     * 分页查询用户操作日志表
     *
     * @param id 用户操作日志表 查询条件
     * @return RestApiResponse<?>
     */
    @GetMapping("/findById")
      @Operation(summary="查询日志")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam(name = "id") String id) {
        SysLogRecord record = sysLogService.findById(id);
        return RestApiResponse.ok(record);
    }

    /**
     * 导出访问日志
     *
     * @param sysLogRecordVo void
     */
    @PostMapping("/export")
      @Operation(summary="导出访问日志")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "export')")
    public void export(SysLogRecordVo sysLogRecordVo) {
        List<SysLogRecordExportVo> list = sysLogService.export(sysLogRecordVo);
        EasyExcelUtils.exportExcel(list, "导出访问日志结果", SysLogRecordExportVo.class, "导出访问日志结果", response);
    }

}
