<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysLogMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 日志记录表 -->
  <resultMap id="BaseResultMap" type="com.jh.common.bean.SysLogRecord" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 是否成功(1成功，0失败) -->
    <result column="FLAG" property="flag" jdbcType="INTEGER" />
    <!-- 备注信息 -->
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <!-- 方法参数 -->
    <result column="PARAMS" property="params" jdbcType="VARCHAR" />
    <!-- 耗时 -->
    <result column="USE_TIMES" property="useTimes" jdbcType="BIGINT" />
    <!-- 模块类型 -->
    <result column="MODULE_TYPE" property="moduleType" jdbcType="VARCHAR" />
    <!-- 模块名称 -->
    <result column="MODULE" property="module" jdbcType="VARCHAR" />
    <!-- 用户名 -->
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <!-- 用户ID -->
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <!-- IP地址 -->
    <result column="IP" property="ip" jdbcType="VARCHAR" />
  </resultMap>
  
  <!-- 导出 -->
	<select id="selectExportData"
		resultType="com.jh.sys.bean.vo.SysLogRecordExportVo">
		select * from sys_log_record where yn=1
		<if
			test="sysLogRecordVo.userName!=null and sysLogRecordVo.userName!=''">
			<bind name="userNameSearch"
				value="'%'+sysLogRecordVo.userName+'%'" />
			and USER_NAME like #{userNameSearch}
		</if>
		<if
			test="sysLogRecordVo.module!=null and sysLogRecordVo.module!=''">
			<bind name="moduleSearch"
				value="'%'+sysLogRecordVo.module+'%'" />
			and MODULE like #{moduleSearch}
		</if>
		<if
			test="sysLogRecordVo.flag!=null and sysLogRecordVo.flag!=''">
			and FLAG=#{sysLogRecordVo.flag}
		</if> 
		<if test="sysLogRecordVo.operTime!=null and sysLogRecordVo.operTime.length == 2">
			<bind name="startTime" value="sysLogRecordVo.operTime[0]"/>
			<bind name="endTime" value="sysLogRecordVo.operTime[1]"/>
			and CREATE_TIME BETWEEN #{startTime} and #{endTime}
		</if>
		order by CREATE_TIME desc
	</select>
</mapper>