package com.jh.inf.service;


import com.github.pagehelper.PageInfo;
import com.jh.inf.bean.InfApp;
import com.jh.inf.bean.vo.InfAppVo;

import java.util.List;
import java.util.Map;

/**
 * 应用令牌秘钥配置Service接口
 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
public interface InfAppService{
	/**
	 *@Description: 保存或更新应用令牌秘钥配置
	 *@param infApp 应用令牌秘钥配置对象
	 *@return String 应用令牌秘钥配置ID
	 *@Author: linqiang
	 */
	String saveOrUpdate(InfApp infApp);

	/**
	 *@Description: 删除应用令牌秘钥配置
	 *@param ids void 应用令牌秘钥配置ID
	 *@Author: linqiang
	 */
	void delete(List<String> ids);

	/**
	 *@Description: 查询应用令牌秘钥配置详情
	 *@param id
	 *@return InfApp
	 *@Author: linqiang
	 */
	InfApp findById(String id);

	/**
	 *@Description: 分页查询应用令牌秘钥配置
	 *@param infAppVo
	 *@return PageInfo<InfApp>
	 *@Author: linqiang
	 */
	PageInfo<InfApp> findPageByQuery(InfAppVo infAppVo);

	/**
	 * 根据appkey 和方法名得到私钥并判断是否有方法权限
	 * @param appKey
	 * @return
	 */
	InfApp findPrivateKey(String appKey);

	/**
	 * 选择权限
	 * @param appMethodList
	 */
	void insertAppKeyMethodList(List<Map<String, String>> appMethodList);

	/**
	 * 根据appkey查询所选择的方法
	 * @param appKey
	 * @return
	 */
	List<String> findMethodByAppKey(String appKey);

	/**
	 * 验证appkey 是否有权限
	 * @param appKey
	 * @param method
	 */
	void checkMethod(String appKey, String method);
}
