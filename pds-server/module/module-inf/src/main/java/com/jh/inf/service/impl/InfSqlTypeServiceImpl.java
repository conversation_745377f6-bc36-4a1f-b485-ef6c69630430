package com.jh.inf.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.CommonConstant;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.inf.bean.InfSqlCode;
import com.jh.inf.bean.InfSqlType;
import com.jh.inf.bean.vo.InfSqlTypeVo;
import com.jh.inf.dao.InfSqlCodeMapper;
import com.jh.inf.dao.InfSqlTypeMapper;
import com.jh.inf.service.InfSqlTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 * @Description: 接口类型树Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
@Service
@Transactional(readOnly = true)
public class InfSqlTypeServiceImpl extends BaseServiceImpl<InfSqlTypeMapper, InfSqlType> implements InfSqlTypeService {
	
	private static final Logger logger = LoggerFactory.getLogger(InfSqlTypeServiceImpl.class);
    @Autowired
    private InfSqlTypeMapper infSqlTypeMapper;
    @Autowired
    private InfSqlCodeMapper infSqlCodeMapper;
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新接口类型树
	 *@param infSqlType 接口类型树对象
	 *@return String 接口类型树ID
	 *@Author: linqiang
	 */
	public String saveOrUpdateInfSqlType(InfSqlType infSqlType) {
		if(infSqlType==null){
			throw new ServiceException("数据异常");
		}
		if(!checkCode(infSqlType.getTypeCode(), infSqlType.getId())) {
			throw new ServiceException("接口类型编码已经存在");
		}
		if(StringUtils.isEmpty(infSqlType.getId())){
			//新增
			infSqlType.setId(UUIDUtils.getUUID());
			infSqlTypeMapper.insertSelective(infSqlType);
		}else{
			//避免页面传入修改
			infSqlType.setYn(null);
			infSqlTypeMapper.updateByPrimaryKeySelective(infSqlType);
		}
		return infSqlType.getId();
	}
	/**
     * @Description 校验CODE重复
     * @param code
     * @param id
     * @return boolean 
     * <AUTHOR>
     */
    private boolean checkCode(String code, String id) {
        Example example = new Example(InfSqlType.class);
        example.createCriteria().andEqualTo("typeCode", code).andEqualTo("yn", CommonConstant.FLAG_YES);
        List<InfSqlType> list = infSqlTypeMapper.selectByExample(example);
        if (list.isEmpty()) {
            return true;
        }
        // 编辑
        // 判断某字符串是否不为空且长度不为0且不由空白符(whitespace)构成，等于!isBlank(String str)
        if (!StringUtils.isEmpty(id)) {
            for (InfSqlType bean : list) {
                if (!bean.getId().equals(id)) {
                    return false;
                }
            }
        } else {
            return false;
        }
        return true;
    }
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除接口类型树
	 *@param id void 接口类型树ID
	 *@Author: linqiang
	 */
	public void deleteInfSqlType(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除
			InfSqlType infSqlType=infSqlTypeMapper.selectByPrimaryKey(id);
			if(infSqlType==null){
				throw new ServiceException("非法请求");
			}
			
			//删除前判断下面是否有sql
			Example example=new Example(InfSqlCode.class);
			Criteria criteria=example.createCriteria();
			criteria.andEqualTo("typeCode", infSqlType.getTypeCode());
			criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
			int typeCount=infSqlCodeMapper.selectCountByExample(example);
			if(typeCount>0) {
				throw new ServiceException("存在sql不能删除");
			}
			
			//逻辑删除
			InfSqlType teminfSqlType=new InfSqlType();
			teminfSqlType.setYn(CommonConstant.FLAG_NO);
			teminfSqlType.setId(infSqlType.getId());
			infSqlTypeMapper.updateByPrimaryKeySelective(teminfSqlType);
		}
	}

	/**
	 *@Description: 查询接口类型树详情
	 *@param id
	 *@return InfSqlType
	 *@Author: linqiang
	 */
    @Override
	public InfSqlType findById(String id) {
		return infSqlTypeMapper.selectByPrimaryKey(id);
	}


	/**
	 *@Description: 分页查询接口类型树
	 *@param infSqlTypeVo
	 *@return PageInfo<InfSqlType>
	 *@Author: linqiang
	 */
	@Override
	public PageInfo<InfSqlType> findPageByQuery(InfSqlTypeVo infSqlTypeVo) {
		PageHelper.startPage(infSqlTypeVo.getPageNum(),infSqlTypeVo.getPageSize());
		Example example=new Example(InfSqlType.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(infSqlTypeVo.getName())){
		//	criteria.andEqualTo(infSqlTypeVo.getName());
		//}
		List<InfSqlType> infSqlTypeList=infSqlTypeMapper.selectByExample(example);
		return new PageInfo<InfSqlType>(infSqlTypeList);
	}

	@Override
	public List<InfSqlType> findAll() {
		Example example=new Example(InfSqlType.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		return infSqlTypeMapper.selectByExample(example);
	}
}
