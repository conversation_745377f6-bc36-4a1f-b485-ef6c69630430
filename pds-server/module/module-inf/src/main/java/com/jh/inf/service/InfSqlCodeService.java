package com.jh.inf.service;

import com.github.pagehelper.PageInfo;
import com.jh.inf.bean.InfSqlCode;
import com.jh.inf.bean.vo.InfSqlCodeVo;

import java.util.List;
/**
 * API接口配置Service接口
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
public interface InfSqlCodeService {
	/**
	 *@Description: 保存或更新API接口配置
	 *@param infSqlCode API接口配置对象
	 *@return String API接口配置ID
	 *@Author: linqiang
	 */
	String saveOrUpdateInfSqlCode(InfSqlCode infSqlCode);
	
	/**
	 *@Description: 删除API接口配置
	 *@param ids void API接口配置ID
	 *@Author: linqiang
	 */
	void deleteInfSqlCode(List<String> ids);

	/**
	 *@Description: 查询API接口配置详情
	 *@param id
	 *@return InfSqlCode
	 *@Author: linqiang
	 */
	InfSqlCode findById(String id);

	/**
	 *@Description: 分页查询API接口配置
	 *@param infSqlCodeVo
	 *@return PageInfo<InfSqlCode>
	 *@Author: linqiang
	 */
	PageInfo<InfSqlCode> findPageByQuery(InfSqlCodeVo infSqlCodeVo);
}
