package com.jh.inf.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.Constant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.inf.bean.InfLogRecord;
import com.jh.inf.bean.vo.InfLogRecordVo;
import com.jh.inf.dao.InfLogRecordMapper;
import com.jh.inf.service.InfLogRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;


//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;

/**
 * @Description: 接口日志记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
@Service
public class InfLogRecordServiceImpl extends BaseServiceImpl<InfLogRecordMapper, InfLogRecord> implements InfLogRecordService {

//	private static final Logger logger = LoggerFactory.getLogger(InfLogRecordServiceImpl.class);
    @Autowired
    private InfLogRecordMapper infLogRecordMapper;

	/**
	 *@Description: 分页查询接口日志记录
	 *@param infLogRecordVo
	 *@return PageInfo<InfLogRecord>
	 *@Author: linqiang
	 */
	@Override
	public PageInfo<InfLogRecord> findPageByQuery(InfLogRecordVo infLogRecordVo) {
		PageHelper.startPage(infLogRecordVo.getPageNum(),infLogRecordVo.getPageSize());
		Example example=new Example(InfLogRecord.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn", Constant.YES);
		//查询条件
		if(!StringUtils.isEmpty(infLogRecordVo.getAppKey())){
			criteria.andEqualTo("appKey",infLogRecordVo.getAppKey());
		}
		if(infLogRecordVo.getFlag()!=null){
			criteria.andEqualTo("flag",infLogRecordVo.getFlag());
		}
		example.orderBy("createTime").desc();
		List<InfLogRecord> infLogRecordList=infLogRecordMapper.selectByExample(example);
		return new PageInfo<InfLogRecord>(infLogRecordList);
	}
}
