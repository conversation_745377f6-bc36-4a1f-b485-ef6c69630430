package com.jh.inf.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.util.security.RSAEncrypt;
import com.jh.inf.bean.InfApp;
import com.jh.inf.bean.vo.InfAppVo;
import com.jh.inf.service.InfAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;

/**
 * 应用令牌秘钥配置Controller
 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
@RestController
@RequestMapping("/inf/app")
@Tag(name = "应用令牌秘钥配置")
public class InfAppController extends BaseController {
    @Autowired
    private InfAppService infAppService;

    private static final String PER_PREFIX = "btn:inf:app:";

    /**
     * @param infApp 应用令牌秘钥配置数据 json
     * @return RestApiResponse<?>
     * @Description: 新增应用令牌秘钥配置
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
      @Operation(summary="新增应用令牌秘钥配置")
    @SystemLogAnnotation(type = "令牌秘钥配置", value = "新增应用令牌秘钥配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> save(@RequestBody InfApp infApp) {
        String id = infAppService.saveOrUpdate(infApp);
        return RestApiResponse.ok(id);
    }

    /**
     * @param infApp 应用令牌秘钥配置数据 json
     * @return RestApiResponse<?>
     * @Description: 修改应用令牌秘钥配置
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
      @Operation(summary="修改应用令牌秘钥配置")
    @SystemLogAnnotation(type = "令牌秘钥配置", value = "修改应用令牌秘钥配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> update(@RequestBody InfApp infApp) {
        String id = infAppService.saveOrUpdate(infApp);
        return RestApiResponse.ok(id);
    }

    /**
     * @param appMethodList 应用令牌秘钥配置数据 json
     * @return RestApiResponse<?>
     * @Description: 修改应用令牌秘钥配置
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/selectMethod")
      @Operation(summary="应用key配置权限")
    @SystemLogAnnotation(type = "令牌秘钥配置", value = "应用key配置权限")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "method')")
    public RestApiResponse<?> selectMethod(@RequestBody List<Map<String, String>> appMethodList) {
        infAppService.insertAppKeyMethodList(appMethodList);
        return RestApiResponse.ok();
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除应用令牌秘钥配置(判断 关联数据是否可以删除)
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
      @Operation(summary="批量删除应用令牌秘钥配置")
    @SystemLogAnnotation(type = "令牌秘钥配置", value = "批量删除应用令牌秘钥配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> delete(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        infAppService.delete(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param appKey
     * @return RestApiResponse<?>
     * @Description: 根据appkey查询所有方法
     * @Author: linqiang
     */
    @GetMapping("/findMethodByAppKey")
      @Operation(summary="根据appkey查询所有方法")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findmethod')")
    public RestApiResponse<?> findMethodByAppKey(@RequestParam("appKey") String appKey) {
        List<String> methods = infAppService.findMethodByAppKey(appKey);
        return RestApiResponse.ok(methods);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询应用令牌秘钥配置详情
     * @Author: linqiang
     */
    @GetMapping("/findById")
      @Operation(summary="查询应用令牌秘钥配置详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        InfApp infApp = infAppService.findById(id);
        return RestApiResponse.ok(infApp);
    }

    /**
     * @param infAppVo 应用令牌秘钥配置 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询应用令牌秘钥配置
     * @Author: linqiang
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询应用令牌秘钥配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody InfAppVo infAppVo) {
        PageInfo<InfApp> infApp = infAppService.findPageByQuery(infAppVo);
        return RestApiResponse.ok(infApp);
    }

    /**
     *@Description: 生成公钥私钥
     *@return RestApiResponse<?>
     *@Author: qiubinbin
     */
    @PostMapping("/rsaEncrypt")
      @Operation(summary="生成公钥私钥")
    public RestApiResponse<?> rsaEncrypt() throws NoSuchAlgorithmException {
        Map<Integer, String> map =  RSAEncrypt.getSecret();
        return RestApiResponse.ok(map);
    }

}
