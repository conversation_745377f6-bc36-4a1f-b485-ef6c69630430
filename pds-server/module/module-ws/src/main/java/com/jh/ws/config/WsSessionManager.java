package com.jh.ws.config;

import com.jh.ws.bean.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;



/**
 * session本机缓存
 * 
 * <AUTHOR>
 *
 */
public class WsSessionManager {
	public static final Logger logger = LoggerFactory.getLogger(WsSessionManager.class);
	/**
	 * 保存连接 session 的地方
	 */
	private static final ConcurrentHashMap<String, UserInfo> SESSION_POOL = new ConcurrentHashMap<>();

	public static ConcurrentHashMap<String, UserInfo> getSessionPool() {
		return SESSION_POOL;
	}
	/**
	 * 添加 session
	 *
	 * @param key
	 */
	public static void add(String key, UserInfo bean) {
		// 添加 session
		SESSION_POOL.put(key, bean);
	}

	/**
	 * 删除 session,会返回删除的 session
	 *
	 * @param key
	 * @return
	 */
	public static UserInfo remove(String key) {
		// 删除 session
		return SESSION_POOL.remove(key);
	}

	/**
	 * 删除并同步关闭连接
	 *
	 * @param key
	 */
	public static UserInfo removeAndClose(String key) {
		UserInfo bean = remove(key);
		if (bean != null) {
			try {
				// 关闭连接
				if(bean.getSession()!=null) {
					bean.getSession().close();
				}
			} catch (IOException e) {
				// todo: 关闭出现异常处理
				logger.error("close session error . ", e);
			}
		}
		return bean;
	}

	/**
	 * 获得 session
	 *
	 * @param key
	 * @return
	 */
	public static UserInfo get(String key) {
		// 获得 session
		return SESSION_POOL.get(key);
	}
}
