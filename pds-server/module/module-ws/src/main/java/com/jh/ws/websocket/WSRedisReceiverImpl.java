package com.jh.ws.websocket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jh.common.exception.ServiceException;
import com.jh.common.redis.RedisReceiver;
import com.jh.ws.bean.MessageInfo;
import com.jh.ws.bean.UserInfo;
import com.jh.ws.config.WsSessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map.Entry;

/**
 * redis 发布订阅消息接收实现类，
 *
 * <AUTHOR>
 */
@Component // 必须把此类交给spring管理
@Service("WSRedisReceiverImpl")
public class WSRedisReceiverImpl implements RedisReceiver {
	public static final Logger logger = LoggerFactory.getLogger(WSRedisReceiverImpl.class);

	/**
	 * 接收到的消息
	 */
	@Override
	public void receiveMessage(String msg) {
		MessageInfo message = JSONObject.parseObject(JSON.parse(msg).toString(), MessageInfo.class);
		logger.debug("收到了前端发送的消息，" + message.getSendUserId() + ":" + message.getSendContent());
		sendForDefault(message);
	}

	/**
	 * 发送消息，默认选项
	 *
	 * @param message
	 */
	private void sendForDefault(MessageInfo message) {
		if (!StringUtils.hasText(message.getDestUserId())) {
			logger.error("未设置接受方！");
			throw new ServiceException("未添加消息接受方");
		}
		/**
		 * 查找当前机器在线用户
		 */
		for (Entry<String, UserInfo> entry : WsSessionManager.getSessionPool().entrySet()) {
			if (entry.getValue().getUserId().equals(message.getDestUserId())) {
				try {
					entry.getValue().getSession().getBasicRemote().sendText(JSON.toJSONString(message));
				} catch (IOException e) {
					logger.error("send websocket error ,userNickeName=" + entry.getValue().getUserNickName(), e);
				}
			}
		}
	}

}
