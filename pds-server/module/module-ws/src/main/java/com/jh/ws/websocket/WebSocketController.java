package com.jh.ws.websocket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jh.common.bean.LoginUser;
import com.jh.common.redis.RedisUtil;
import com.jh.common.util.security.JwtUtils;
import com.jh.constant.RedisConstant;
import com.jh.ws.bean.MessageInfo;
import com.jh.ws.bean.UserInfo;
import com.jh.ws.config.WsSessionManager;
import com.jh.ws.util.SpringUtil;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.net.InetSocketAddress;
import java.nio.channels.SocketChannel;
import java.util.Date;
/**
 * websocket对外主入口
 *
 * <AUTHOR>
 */
@Service
@ServerEndpoint(value = "/ws/{token}" ,configurator = CustomConfigurator.class)
public class WebSocketController {
    public static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);

    private RedisUtil redisUtil;
    private StringRedisTemplate stringRedisTemplate;
    public UserInfo getLoginUser(String token) {
        Object loginUser = getRedisUtil().get(getTokenKey(token));
        UserInfo userInfo=null;
        if (loginUser != null) {
            if (loginUser instanceof LoginUser) {
                LoginUser user = (LoginUser) loginUser;
                userInfo=new UserInfo();
                userInfo.setUserId(user.getId());
                userInfo.setToken(user.getToken());
                userInfo.setUserName(user.getUsername());
                userInfo.setUserNickName(user.getNickname());
            }
        }

        return userInfo;
    }


    private String getTokenKey(String token) {
        return RedisConstant.SYS_REDIS+"tokens:" + token;
    }
    /**
     * 建立连接成功调用
     */
    @OnOpen
    public void onOpen(Session session,  @PathParam(value = "token") String token) throws IOException {
        //token 就是用户登录时的token 如果是游客为test
        RemoteEndpoint.Async async = session.getAsyncRemote();
        // 获取WebSocket握手请求 并获取ip
        SocketChannel sc = (SocketChannel) getFieldInstance(async,"base#socketWrapper#socket#sc");
        InetSocketAddress addr =(InetSocketAddress)sc.getRemoteAddress();
        try {
            // 获取当前登录用户信息
            token= JwtUtils.getUserKey(token);
            UserInfo user = getLoginUser(token);
            if (user == null) {
                session.getAsyncRemote().sendText("{\"code\":\"nologin\",\"message\":\"您的登录信息已过期，请重新登录\"}");
                session.close();
                return;
            }
//            session.getAsyncRemote().sendText("{\"session\":\"" + user.getUserId() + "\",\"name\":\"" + user.getUserNickName() + "\"}");
            user.setSession(session);
            WsSessionManager.add(session.getId(), user);
        } catch (Exception e) {
            try {
                session.close();
            } catch (IOException e1) {
                logger.error("userId is null close session .error:", e1);
            }
        }
    }

    /**
     * 关闭连接时调用
     */
    @OnClose
    public void onClose(Session session) {
        UserInfo bean = WsSessionManager.removeAndClose(session.getId());
        if (bean != null) {
            logger.debug("{}断开webSocket连接！", bean.getUserNickName());
        }
    }

    /**
     * 收到客户端信息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            UserInfo bean = WsSessionManager.get(session.getId());
            if (bean == null) {
                session.close();
                return;
            }
            if ("success".equals(message)) {
                session.getBasicRemote().sendText(message);
                return;
            }
            MessageInfo wsMsg = JSON.parseObject(message, MessageInfo.class);
            wsMsg.setSendTime(new Date());
            getStringRedisTemplate().convertAndSend(RedisConstant.REDIS_TOPIC_WEBSOCKET,
                    JSONObject.toJSONString(wsMsg));
        } catch (Exception e) {
            logger.error("收到客户端消息异常", e);
        }
    }

    /**
     * 错误时调用
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        logger.error("WebSocket发生错误:", throwable);
        WsSessionManager.removeAndClose(session.getId());
    }



    /**
     * 得到发送mq连接
     *
     * @return
     */
    private StringRedisTemplate getStringRedisTemplate() {
        if (stringRedisTemplate == null) {
            stringRedisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
        }
        return stringRedisTemplate;
    }
    private RedisUtil getRedisUtil() {
        if (redisUtil == null) {
            redisUtil = SpringUtil.getBean(RedisUtil.class);
        }
        return redisUtil;
    }


    private static Object getFieldInstance(Object obj, String fieldPath) {
        String fields[] = fieldPath.split("#");
        for (String field : fields) {
            obj = getField(obj, obj.getClass(), field);
            if (obj == null) {
                return null;
            }
        }

        return obj;
    }

    private static Object getField(Object obj, Class<?> clazz, String fieldName) {
        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                Field field;
                field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (Exception e) {
            }
        }

        return null;
    }
}
